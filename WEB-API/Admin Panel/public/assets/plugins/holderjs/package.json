{"name": "Holder", "version": "2.4.1", "summary": "client side image placeholders", "description": "Holder uses SVG to render image placeholders entirely in browser.", "author": {"name": "<PERSON>", "url": "http://imsky.co"}, "homepage": "http://imsky.github.io/holder", "license": {"type": "MIT", "url": "http://opensource.org/licenses/MIT"}, "keywords": ["images", "placeholders", "client-side", "canvas", "generation", "development", "svg"], "main": "holder.js", "repository": {"type": "git", "url": "git://github.com/imsky/holder.git"}, "bugs": {"url": "https://github.com/imsky/holder/issues"}, "devDependencies": {"moment": "^2.6.0", "gulp": "~3", "gulp-uglify": "^0.3.0", "gulp-concat": "^2.2.0", "gulp-header": "^1.0.2", "gulp-jshint": "^1.6.1", "gulp-todo": "^0.3.8", "gulp-util": "~3"}}