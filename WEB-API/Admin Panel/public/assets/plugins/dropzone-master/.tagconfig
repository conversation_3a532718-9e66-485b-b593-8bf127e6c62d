{"files": [{"name": "src/dropzone.coffee", "regexs": ["Dropzone.version = \"###\""]}, {"name": "dist/dropzone.js", "regexs": ["version = \"###\""]}, {"name": "dist/min/dropzone.min.js", "regexs": ["version=\"###\""]}, {"name": "dist/dropzone-amd-module.js", "regexs": ["version = \"###\""]}, {"name": "dist/min/dropzone-amd-module.min.js", "regexs": ["version=\"###\""]}, {"name": "package.json", "regexs": ["\"version\": \"###\""]}, {"name": "component.json", "regexs": ["\"version\": \"###\""]}, {"name": "bower.json", "regexs": ["\"version\": \"###\""]}]}