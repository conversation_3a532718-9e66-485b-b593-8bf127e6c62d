{"name": "dropzone", "version": "4.3.0", "description": "Handles drag and drop of files for you.", "keywords": ["dragndrop", "drag and drop", "file upload", "upload"], "homepage": "http://www.dropzonejs.com", "main": "./dist/dropzone.js", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "http://www.matiasmeno.com"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "http://www.matiasmeno.com"}], "scripts": {"test": "./test.sh"}, "bugs": {"email": "<EMAIL>", "url": "https://github.com/enyo/dropzone/issues"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/MIT"}], "repository": {"type": "git", "url": "https://github.com/enyo/dropzone.git"}, "dependencies": {}, "devDependencies": {"chai": "1.7.x", "grunt": "^0.4.4", "grunt-contrib-coffee": "^0.10.1", "grunt-contrib-concat": "^0.4.0", "grunt-contrib-sass": "^0.8.1", "grunt-contrib-uglify": "^0.4.0", "grunt-contrib-watch": "^0.6.1", "mocha": "^1.18.2", "mocha-phantomjs": "^3.3.2", "sinon": "1.9.1"}}