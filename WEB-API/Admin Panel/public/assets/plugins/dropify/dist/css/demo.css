/*!
 * =============================================================
 * dropify v0.2.0 - Override your input files with style.
 * https://github.com/JeremyFagis/dropify
 *
 * (c) 2016 - Jeremy FAGIS <<EMAIL>> (http://fagis.fr)
 * =============================================================
 */

* {
  box-sizing: border-box;
  outline: none; }

*, *::before, *::after, *:before, *:after {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale; }

/*! normalize.css v1.1.3 | MIT License | git.io/normalize */
/* ==========================================================================
   HTML5 display definitions
   ========================================================================== */
/**
 * Correct `block` display not defined in IE 6/7/8/9 and Firefox 3.
 */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
nav,
section,
summary {
  display: block; }

/**
 * Correct `inline-block` display not defined in IE 6/7/8/9 and Firefox 3.
 */
audio,
canvas,
video {
  display: inline-block;
  *display: inline;
  *zoom: 1; }

/**
 * Prevent modern browsers from displaying `audio` without controls.
 * Remove excess height in iOS 5 devices.
 */
audio:not([controls]) {
  display: none;
  height: 0; }

/**
 * Address styling not present in IE 7/8/9, Firefox 3, and Safari 4.
 * Known issue: no IE 6 support.
 */
[hidden] {
  display: none; }

/* ==========================================================================
   Base
   ========================================================================== */
/**
 * 1. Correct text resizing oddly in IE 6/7 when body `font-size` is set using
 *    `em` units.
 * 2. Prevent iOS text size adjust after orientation change, without disabling
 *    user zoom.
 */
html {
  font-size: 100%;
  /* 1 */
  -ms-text-size-adjust: 100%;
  /* 2 */
  -webkit-text-size-adjust: 100%;
  /* 2 */ }

/**
 * Address `font-family` inconsistency between `textarea` and other form
 * elements.
 */
html,
button,
input,
select,
textarea {
  font-family: sans-serif; }

/**
 * Address margins handled incorrectly in IE 6/7.
 */
body {
  margin: 0; }

/* ==========================================================================
   Links
   ========================================================================== */
/**
 * Address `outline` inconsistency between Chrome and other browsers.
 */
a:focus {
  outline: thin dotted; }

/**
 * Improve readability when focused and also mouse hovered in all browsers.
 */
a:active,
a:hover {
  outline: 0; }

/* ==========================================================================
   Typography
   ========================================================================== */
/**
 * Address font sizes and margins set differently in IE 6/7.
 * Address font sizes within `section` and `article` in Firefox 4+, Safari 5,
 * and Chrome.
 */
h1 {
  font-size: 2em;
  margin: 0.67em 0; }

h2 {
  font-size: 1.5em;
  margin: 0.83em 0; }

h3 {
  font-size: 1.17em;
  margin: 1em 0; }

h4 {
  font-size: 1em;
  margin: 1.33em 0; }

h5 {
  font-size: 0.83em;
  margin: 1.67em 0; }

h6 {
  font-size: 0.67em;
  margin: 2.33em 0; }

/**
 * Address styling not present in IE 7/8/9, Safari 5, and Chrome.
 */
abbr[title] {
  border-bottom: 1px dotted; }

/**
 * Address style set to `bolder` in Firefox 3+, Safari 4/5, and Chrome.
 */
b,
strong {
  font-weight: bold; }

blockquote {
  margin: 1em 40px; }

/**
 * Address styling not present in Safari 5 and Chrome.
 */
dfn {
  font-style: italic; }

/**
 * Address differences between Firefox and other browsers.
 * Known issue: no IE 6/7 normalization.
 */
hr {
  box-sizing: content-box;
  height: 0; }

/**
 * Address styling not present in IE 6/7/8/9.
 */
mark {
  background: #ff0;
  color: #000; }

/**
 * Address margins set differently in IE 6/7.
 */
p,
pre {
  margin: 1em 0; }

/**
 * Correct font family set oddly in IE 6, Safari 4/5, and Chrome.
 */
code,
kbd,
pre,
samp {
  font-family: monospace, serif;
  _font-family: 'courier new', monospace;
  font-size: 1em; }

/**
 * Improve readability of pre-formatted text in all browsers.
 */
pre {
  white-space: pre;
  white-space: pre-wrap;
  word-wrap: break-word; }

/**
 * Address CSS quotes not supported in IE 6/7.
 */
q {
  quotes: none; }

/**
 * Address `quotes` property not supported in Safari 4.
 */
q:before,
q:after {
  content: '';
  content: none; }

/**
 * Address inconsistent and variable font size in all browsers.
 */
small {
  font-size: 80%; }

/**
 * Prevent `sub` and `sup` affecting `line-height` in all browsers.
 */
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline; }

sup {
  top: -0.5em; }

sub {
  bottom: -0.25em; }

/* ==========================================================================
   Lists
   ========================================================================== */
/**
 * Address margins set differently in IE 6/7.
 */
dl,
menu,
ol,
ul {
  margin: 1em 0; }

dd {
  margin: 0 0 0 40px; }

/**
 * Address paddings set differently in IE 6/7.
 */
menu,
ol,
ul {
  padding: 0 0 0 40px; }

/**
 * Correct list images handled incorrectly in IE 7.
 */
nav ul,
nav ol {
  list-style: none;
  list-style-image: none; }

/* ==========================================================================
   Embedded content
   ========================================================================== */
/**
 * 1. Remove border when inside `a` element in IE 6/7/8/9 and Firefox 3.
 * 2. Improve image quality when scaled in IE 7.
 */
img {
  border: 0;
  /* 1 */
  -ms-interpolation-mode: bicubic;
  /* 2 */ }

/**
 * Correct overflow displayed oddly in IE 9.
 */
svg:not(:root) {
  overflow: hidden; }

/* ==========================================================================
   Figures
   ========================================================================== */
/**
 * Address margin not present in IE 6/7/8/9, Safari 5, and Opera 11.
 */
figure {
  margin: 0; }

/* ==========================================================================
   Forms
   ========================================================================== */
/**
 * Correct margin displayed oddly in IE 6/7.
 */
form {
  margin: 0; }

/**
 * Define consistent border, margin, and padding.
 */
fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em; }

/**
 * 1. Correct color not being inherited in IE 6/7/8/9.
 * 2. Correct text not wrapping in Firefox 3.
 * 3. Correct alignment displayed oddly in IE 6/7.
 */
legend {
  border: 0;
  /* 1 */
  padding: 0;
  white-space: normal;
  /* 2 */
  *margin-left: -7px;
  /* 3 */ }

/**
 * 1. Correct font size not being inherited in all browsers.
 * 2. Address margins set differently in IE 6/7, Firefox 3+, Safari 5,
 *    and Chrome.
 * 3. Improve appearance and consistency in all browsers.
 */
button,
input,
select,
textarea {
  font-size: 100%;
  /* 1 */
  margin: 0;
  /* 2 */
  vertical-align: baseline;
  /* 3 */
  *vertical-align: middle;
  /* 3 */ }

/**
 * Address Firefox 3+ setting `line-height` on `input` using `!important` in
 * the UA stylesheet.
 */
button,
input {
  line-height: normal; }

/**
 * Address inconsistent `text-transform` inheritance for `button` and `select`.
 * All other form control elements do not inherit `text-transform` values.
 * Correct `button` style inheritance in Chrome, Safari 5+, and IE 6+.
 * Correct `select` style inheritance in Firefox 4+ and Opera.
 */
button,
select {
  text-transform: none; }

/**
 * 1. Avoid the WebKit bug in Android 4.0.* where (2) destroys native `audio`
 *    and `video` controls.
 * 2. Correct inability to style clickable `input` types in iOS.
 * 3. Improve usability and consistency of cursor style between image-type
 *    `input` and others.
 * 4. Remove inner spacing in IE 7 without affecting normal text inputs.
 *    Known issue: inner spacing remains in IE 6.
 */
button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
  -webkit-appearance: button;
  /* 2 */
  cursor: pointer;
  /* 3 */
  *overflow: visible;
  /* 4 */ }

/**
 * Re-set default cursor for disabled elements.
 */
button[disabled],
html input[disabled] {
  cursor: default; }

/**
 * 1. Address box sizing set to content-box in IE 8/9.
 * 2. Remove excess padding in IE 8/9.
 * 3. Remove excess padding in IE 7.
 *    Known issue: excess padding remains in IE 6.
 */
input[type="checkbox"],
input[type="radio"] {
  box-sizing: border-box;
  /* 1 */
  padding: 0;
  /* 2 */
  *height: 13px;
  /* 3 */
  *width: 13px;
  /* 3 */ }

/**
 * 1. Address `appearance` set to `searchfield` in Safari 5 and Chrome.
 * 2. Address `box-sizing` set to `border-box` in Safari 5 and Chrome
 *    (include `-moz` to future-proof).
 */
input[type="search"] {
  -webkit-appearance: textfield;
  /* 1 */
  /* 2 */
  box-sizing: content-box; }

/**
 * Remove inner padding and search cancel button in Safari 5 and Chrome
 * on OS X.
 */
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none; }

/**
 * Remove inner padding and border in Firefox 3+.
 */
button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0; }

/**
 * 1. Remove default vertical scrollbar in IE 6/7/8/9.
 * 2. Improve readability and alignment in all browsers.
 */
textarea {
  overflow: auto;
  /* 1 */
  vertical-align: top;
  /* 2 */ }

/* ==========================================================================
   Tables
   ========================================================================== */
/**
 * Remove most spacing between table cells.
 */
table {
  border-collapse: collapse;
  border-spacing: 0; }

.text-left {
  text-align: left; }

.text-right {
  text-align: right; }

.text-center {
  text-align: center; }

.text-justify {
  text-align: justify; }

.pull-right {
  float: right !important; }

.pull-left {
  float: left !important; }

.hidden {
  display: none; }

.hide-text {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0; }

.strong, .bold {
  font-weight: 700; }

.italic {
  font-style: italic; }

.clearfix {
  *zoom: 1; }
  .clearfix:before, .clearfix:after {
    display: table;
    content: ""; }
  .clearfix:after {
    clear: both; }

hr {
  margin-top: 27px;
  margin-bottom: 27px;
  border: 0;
  border-top: 1px solid #CCC; }
  hr.big {
    border-top: 2px solid #DDD;
    margin-top: 54px;
    margin-bottom: 54px; }

.container {
  width: 100%;
  max-width: 1140px;
  margin-right: auto;
  margin-left: auto;
  padding-left: 15px;
  padding-right: 15px;
  *zoom: 1; }
  .container:before, .container:after {
    display: table;
    content: ""; }
  .container:after {
    clear: both; }

.row {
  margin-left: -15px;
  margin-right: -15px;
  *zoom: 1; }
  .row:before, .row:after {
    display: table;
    content: ""; }
  .row:after {
    clear: both; }

.col-xs-1, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9, .col-xs-10, .col-xs-11, .col-xs-12, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12, .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12 {
  position: relative;
  min-height: 1px;
  padding-left: 15px;
  padding-right: 15px;
  padding-bottom: 30px; }
  .col-xs-1.no-padding-bottom, .col-xs-1.no-pb, .col-xs-2.no-padding-bottom, .col-xs-2.no-pb, .col-xs-3.no-padding-bottom, .col-xs-3.no-pb, .col-xs-4.no-padding-bottom, .col-xs-4.no-pb, .col-xs-5.no-padding-bottom, .col-xs-5.no-pb, .col-xs-6.no-padding-bottom, .col-xs-6.no-pb, .col-xs-7.no-padding-bottom, .col-xs-7.no-pb, .col-xs-8.no-padding-bottom, .col-xs-8.no-pb, .col-xs-9.no-padding-bottom, .col-xs-9.no-pb, .col-xs-10.no-padding-bottom, .col-xs-10.no-pb, .col-xs-11.no-padding-bottom, .col-xs-11.no-pb, .col-xs-12.no-padding-bottom, .col-xs-12.no-pb, .col-sm-1.no-padding-bottom, .col-sm-1.no-pb, .col-sm-2.no-padding-bottom, .col-sm-2.no-pb, .col-sm-3.no-padding-bottom, .col-sm-3.no-pb, .col-sm-4.no-padding-bottom, .col-sm-4.no-pb, .col-sm-5.no-padding-bottom, .col-sm-5.no-pb, .col-sm-6.no-padding-bottom, .col-sm-6.no-pb, .col-sm-7.no-padding-bottom, .col-sm-7.no-pb, .col-sm-8.no-padding-bottom, .col-sm-8.no-pb, .col-sm-9.no-padding-bottom, .col-sm-9.no-pb, .col-sm-10.no-padding-bottom, .col-sm-10.no-pb, .col-sm-11.no-padding-bottom, .col-sm-11.no-pb, .col-sm-12.no-padding-bottom, .col-sm-12.no-pb, .col-md-1.no-padding-bottom, .col-md-1.no-pb, .col-md-2.no-padding-bottom, .col-md-2.no-pb, .col-md-3.no-padding-bottom, .col-md-3.no-pb, .col-md-4.no-padding-bottom, .col-md-4.no-pb, .col-md-5.no-padding-bottom, .col-md-5.no-pb, .col-md-6.no-padding-bottom, .col-md-6.no-pb, .col-md-7.no-padding-bottom, .col-md-7.no-pb, .col-md-8.no-padding-bottom, .col-md-8.no-pb, .col-md-9.no-padding-bottom, .col-md-9.no-pb, .col-md-10.no-padding-bottom, .col-md-10.no-pb, .col-md-11.no-padding-bottom, .col-md-11.no-pb, .col-md-12.no-padding-bottom, .col-md-12.no-pb, .col-lg-1.no-padding-bottom, .col-lg-1.no-pb, .col-lg-2.no-padding-bottom, .col-lg-2.no-pb, .col-lg-3.no-padding-bottom, .col-lg-3.no-pb, .col-lg-4.no-padding-bottom, .col-lg-4.no-pb, .col-lg-5.no-padding-bottom, .col-lg-5.no-pb, .col-lg-6.no-padding-bottom, .col-lg-6.no-pb, .col-lg-7.no-padding-bottom, .col-lg-7.no-pb, .col-lg-8.no-padding-bottom, .col-lg-8.no-pb, .col-lg-9.no-padding-bottom, .col-lg-9.no-pb, .col-lg-10.no-padding-bottom, .col-lg-10.no-pb, .col-lg-11.no-padding-bottom, .col-lg-11.no-pb, .col-lg-12.no-padding-bottom, .col-lg-12.no-pb {
    padding-bottom: 0; }

.col-xs-1, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9, .col-xs-10, .col-xs-11 {
  float: left; }

.col-xs-1 {
  width: 8.33333%; }

.col-xs-2 {
  width: 16.66667%; }

.col-xs-3 {
  width: 25%; }

.col-xs-4 {
  width: 33.33333%; }

.col-xs-5 {
  width: 41.66667%; }

.col-xs-6 {
  width: 50%; }

.col-xs-7 {
  width: 58.33333%; }

.col-xs-8 {
  width: 66.66667%; }

.col-xs-9 {
  width: 75%; }

.col-xs-10 {
  width: 83.33333%; }

.col-xs-11 {
  width: 91.66667%; }

.col-xs-12 {
  width: 100%; }

@media (min-width: 768px) {
  .col-sm-1 {
    float: left; }
  .col-sm-1 {
    width: 8.33333%; }
  .col-sm-push-1 {
    left: 8.33333%; }
  .col-sm-pull-1 {
    right: 8.33333%; }
  .col-sm-offset-1 {
    margin-left: 8.33333%; }
  .col-sm-2 {
    float: left; }
  .col-sm-2 {
    width: 16.66667%; }
  .col-sm-push-2 {
    left: 16.66667%; }
  .col-sm-pull-2 {
    right: 16.66667%; }
  .col-sm-offset-2 {
    margin-left: 16.66667%; }
  .col-sm-3 {
    float: left; }
  .col-sm-3 {
    width: 25%; }
  .col-sm-push-3 {
    left: 25%; }
  .col-sm-pull-3 {
    right: 25%; }
  .col-sm-offset-3 {
    margin-left: 25%; }
  .col-sm-4 {
    float: left; }
  .col-sm-4 {
    width: 33.33333%; }
  .col-sm-push-4 {
    left: 33.33333%; }
  .col-sm-pull-4 {
    right: 33.33333%; }
  .col-sm-offset-4 {
    margin-left: 33.33333%; }
  .col-sm-5 {
    float: left; }
  .col-sm-5 {
    width: 41.66667%; }
  .col-sm-push-5 {
    left: 41.66667%; }
  .col-sm-pull-5 {
    right: 41.66667%; }
  .col-sm-offset-5 {
    margin-left: 41.66667%; }
  .col-sm-6 {
    float: left; }
  .col-sm-6 {
    width: 50%; }
  .col-sm-push-6 {
    left: 50%; }
  .col-sm-pull-6 {
    right: 50%; }
  .col-sm-offset-6 {
    margin-left: 50%; }
  .col-sm-7 {
    float: left; }
  .col-sm-7 {
    width: 58.33333%; }
  .col-sm-push-7 {
    left: 58.33333%; }
  .col-sm-pull-7 {
    right: 58.33333%; }
  .col-sm-offset-7 {
    margin-left: 58.33333%; }
  .col-sm-8 {
    float: left; }
  .col-sm-8 {
    width: 66.66667%; }
  .col-sm-push-8 {
    left: 66.66667%; }
  .col-sm-pull-8 {
    right: 66.66667%; }
  .col-sm-offset-8 {
    margin-left: 66.66667%; }
  .col-sm-9 {
    float: left; }
  .col-sm-9 {
    width: 75%; }
  .col-sm-push-9 {
    left: 75%; }
  .col-sm-pull-9 {
    right: 75%; }
  .col-sm-offset-9 {
    margin-left: 75%; }
  .col-sm-10 {
    float: left; }
  .col-sm-10 {
    width: 83.33333%; }
  .col-sm-push-10 {
    left: 83.33333%; }
  .col-sm-pull-10 {
    right: 83.33333%; }
  .col-sm-offset-10 {
    margin-left: 83.33333%; }
  .col-sm-11 {
    float: left; }
  .col-sm-11 {
    width: 91.66667%; }
  .col-sm-push-11 {
    left: 91.66667%; }
  .col-sm-pull-11 {
    right: 91.66667%; }
  .col-sm-offset-11 {
    margin-left: 91.66667%; }
  .col-sm-push-0 {
    left: auto; }
  .col-sm-pull-0 {
    right: auto; }
  .col-sm-offset-0 {
    margin-left: 0; }
  .col-sm-12 {
    width: 100%; } }

@media (min-width: 992px) {
  .col-md-1 {
    float: left; }
  .col-md-1 {
    width: 8.33333%; }
  .col-md-push-1 {
    left: 8.33333%; }
  .col-md-pull-1 {
    right: 8.33333%; }
  .col-md-offset-1 {
    margin-left: 8.33333%; }
  .col-md-2 {
    float: left; }
  .col-md-2 {
    width: 16.66667%; }
  .col-md-push-2 {
    left: 16.66667%; }
  .col-md-pull-2 {
    right: 16.66667%; }
  .col-md-offset-2 {
    margin-left: 16.66667%; }
  .col-md-3 {
    float: left; }
  .col-md-3 {
    width: 25%; }
  .col-md-push-3 {
    left: 25%; }
  .col-md-pull-3 {
    right: 25%; }
  .col-md-offset-3 {
    margin-left: 25%; }
  .col-md-4 {
    float: left; }
  .col-md-4 {
    width: 33.33333%; }
  .col-md-push-4 {
    left: 33.33333%; }
  .col-md-pull-4 {
    right: 33.33333%; }
  .col-md-offset-4 {
    margin-left: 33.33333%; }
  .col-md-5 {
    float: left; }
  .col-md-5 {
    width: 41.66667%; }
  .col-md-push-5 {
    left: 41.66667%; }
  .col-md-pull-5 {
    right: 41.66667%; }
  .col-md-offset-5 {
    margin-left: 41.66667%; }
  .col-md-6 {
    float: left; }
  .col-md-6 {
    width: 50%; }
  .col-md-push-6 {
    left: 50%; }
  .col-md-pull-6 {
    right: 50%; }
  .col-md-offset-6 {
    margin-left: 50%; }
  .col-md-7 {
    float: left; }
  .col-md-7 {
    width: 58.33333%; }
  .col-md-push-7 {
    left: 58.33333%; }
  .col-md-pull-7 {
    right: 58.33333%; }
  .col-md-offset-7 {
    margin-left: 58.33333%; }
  .col-md-8 {
    float: left; }
  .col-md-8 {
    width: 66.66667%; }
  .col-md-push-8 {
    left: 66.66667%; }
  .col-md-pull-8 {
    right: 66.66667%; }
  .col-md-offset-8 {
    margin-left: 66.66667%; }
  .col-md-9 {
    float: left; }
  .col-md-9 {
    width: 75%; }
  .col-md-push-9 {
    left: 75%; }
  .col-md-pull-9 {
    right: 75%; }
  .col-md-offset-9 {
    margin-left: 75%; }
  .col-md-10 {
    float: left; }
  .col-md-10 {
    width: 83.33333%; }
  .col-md-push-10 {
    left: 83.33333%; }
  .col-md-pull-10 {
    right: 83.33333%; }
  .col-md-offset-10 {
    margin-left: 83.33333%; }
  .col-md-11 {
    float: left; }
  .col-md-11 {
    width: 91.66667%; }
  .col-md-push-11 {
    left: 91.66667%; }
  .col-md-pull-11 {
    right: 91.66667%; }
  .col-md-offset-11 {
    margin-left: 91.66667%; }
  .col-md-push-0 {
    left: auto; }
  .col-md-pull-0 {
    right: auto; }
  .col-md-offset-0 {
    margin-left: 0; }
  .col-md-12 {
    width: 100%; } }

@media (min-width: 1140px) {
  .col-lg-1 {
    float: left; }
  .col-lg-1 {
    width: 8.33333%; }
  .col-lg-push-1 {
    left: 8.33333%; }
  .col-lg-pull-1 {
    right: 8.33333%; }
  .col-lg-offset-1 {
    margin-left: 8.33333%; }
  .col-lg-2 {
    float: left; }
  .col-lg-2 {
    width: 16.66667%; }
  .col-lg-push-2 {
    left: 16.66667%; }
  .col-lg-pull-2 {
    right: 16.66667%; }
  .col-lg-offset-2 {
    margin-left: 16.66667%; }
  .col-lg-3 {
    float: left; }
  .col-lg-3 {
    width: 25%; }
  .col-lg-push-3 {
    left: 25%; }
  .col-lg-pull-3 {
    right: 25%; }
  .col-lg-offset-3 {
    margin-left: 25%; }
  .col-lg-4 {
    float: left; }
  .col-lg-4 {
    width: 33.33333%; }
  .col-lg-push-4 {
    left: 33.33333%; }
  .col-lg-pull-4 {
    right: 33.33333%; }
  .col-lg-offset-4 {
    margin-left: 33.33333%; }
  .col-lg-5 {
    float: left; }
  .col-lg-5 {
    width: 41.66667%; }
  .col-lg-push-5 {
    left: 41.66667%; }
  .col-lg-pull-5 {
    right: 41.66667%; }
  .col-lg-offset-5 {
    margin-left: 41.66667%; }
  .col-lg-6 {
    float: left; }
  .col-lg-6 {
    width: 50%; }
  .col-lg-push-6 {
    left: 50%; }
  .col-lg-pull-6 {
    right: 50%; }
  .col-lg-offset-6 {
    margin-left: 50%; }
  .col-lg-7 {
    float: left; }
  .col-lg-7 {
    width: 58.33333%; }
  .col-lg-push-7 {
    left: 58.33333%; }
  .col-lg-pull-7 {
    right: 58.33333%; }
  .col-lg-offset-7 {
    margin-left: 58.33333%; }
  .col-lg-8 {
    float: left; }
  .col-lg-8 {
    width: 66.66667%; }
  .col-lg-push-8 {
    left: 66.66667%; }
  .col-lg-pull-8 {
    right: 66.66667%; }
  .col-lg-offset-8 {
    margin-left: 66.66667%; }
  .col-lg-9 {
    float: left; }
  .col-lg-9 {
    width: 75%; }
  .col-lg-push-9 {
    left: 75%; }
  .col-lg-pull-9 {
    right: 75%; }
  .col-lg-offset-9 {
    margin-left: 75%; }
  .col-lg-10 {
    float: left; }
  .col-lg-10 {
    width: 83.33333%; }
  .col-lg-push-10 {
    left: 83.33333%; }
  .col-lg-pull-10 {
    right: 83.33333%; }
  .col-lg-offset-10 {
    margin-left: 83.33333%; }
  .col-lg-11 {
    float: left; }
  .col-lg-11 {
    width: 91.66667%; }
  .col-lg-push-11 {
    left: 91.66667%; }
  .col-lg-pull-11 {
    right: 91.66667%; }
  .col-lg-offset-11 {
    margin-left: 91.66667%; }
  .col-lg-push-0 {
    left: auto; }
  .col-lg-pull-0 {
    right: auto; }
  .col-lg-offset-0 {
    margin-left: 0; }
  .col-lg-12 {
    width: 100%; } }

a, a:visited {
  color: #666;
  text-decoration: none; }

a:hover {
  text-decoration: underline; }

h1, h2, h3, h4 {
  font-family: "Roboto Condensed", "Helvetica Neue", "Helvetica";
  font-weight: 400;
  line-height: 1.4;
  color: #444;
  letter-spacing: -0.04em; }
  h1 small,
  h1 .small, h2 small,
  h2 .small, h3 small,
  h3 .small, h4 small,
  h4 .small {
    font-weight: normal;
    line-height: 1;
    color: #b3b3b3; }
  h1 a, h2 a, h3 a, h4 a {
    color: #444; }

h1, h2, h3, h4 {
  margin-top: 20px;
  margin-bottom: 10px; }
  h1 small,
  h1 .small, h2 small,
  h2 .small, h3 small,
  h3 .small, h4 small,
  h4 .small {
    font-size: 65%; }

h1 {
  font-size: 30px;
  text-transform: uppercase;
  text-align: center;
  margin-bottom: 25px;
  position: relative;
  padding: 0 0 20px 0; }
  h1::after {
    content: '';
    position: absolute;
    width: 60px;
    height: 3px;
    background-color: #444;
    left: 50%;
    -webkit-transform: translateX(-50%);
            transform: translateX(-50%);
    bottom: 0; }

h2 {
  font-size: 26px;
  margin-bottom: 20px; }

h3 {
  font-size: 24px; }

h4 {
  font-size: 20px; }

body {
  background: #F8F8F8;
  font-family: "Roboto", "Helvetica Neue", "Helvetica";
  font-size: 15px;
  line-height: 27px;
  color: #666; }

header#header {
  background: #1C252C;
  color: #FFD270;
  font-family: "Roboto Condensed", "Helvetica Neue", "Helvetica";
  font-weight: 700;
  letter-spacing: -0.04em;
  text-align: center;
  font-size: 70px;
  line-height: 60px;
  padding: 50px 30px;
  margin: 0 0 30px 0; }
  header#header h1 {
    color: #FFD270;
    font-size: 70px;
    line-height: 90px;
    font-weight: 700; }
    header#header h1::after {
      background: rgba(255, 210, 112, 0.75); }
  header#header h2 {
    color: rgba(255, 210, 112, 0.5); }

footer#footer {
  background: #DDD;
  color: #888;
  padding: 30px 0;
  margin-top: 30px; }
  footer#footer a, footer#footer a:visited {
    color: #888; }

label {
  font-weight: bold;
  display: block;
  margin-bottom: 5px; }

p.help {
  color: #999; }

#toggleDropify {
  font-size: 12px;
  text-transform: uppercase;
  background: #DDD;
  color: #888;
  font-weight: bold;
  border: 0;
  padding: 6px 10px;
  border-radius: 4px;
  margin-left: 10px;
  -webkit-transition: background 0.1s linear;
  transition: background 0.1s linear; }
  #toggleDropify:hover {
    background: #EEE; }

@media (max-width: 480px) {
  header#header {
    padding: 30px 15px; }
    header#header h1 {
      font-size: 44px;
      line-height: 70px;
      padding-bottom: 15px;
      margin-bottom: 15px; }
    header#header h2 {
      font-size: 20px; } }
