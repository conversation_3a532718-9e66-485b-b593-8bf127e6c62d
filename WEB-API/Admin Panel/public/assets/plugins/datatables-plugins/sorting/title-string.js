/**
 * Just like the _hidden title numeric sorting_ plug-in, this sorting plug-in
 * will take the information to be sorted on from the title attribute of a span
 * element. The only difference is that it is string based sorting rather than
 * numeric.
 *
 * Note that the HTML5 `data-sort` attribute can be [used to supply sorting data
 * to DataTables](//datatables.net/manual/orthogonal-data) and is preferable to
 * using this method, which is therefore marked as deprecated.
 * 
 *  @name Hidden title string sorting
 *  @summary Sort data as a string based on an attribute on an empty element.
 *  <AUTHOR>
 *  @deprecated
 *
 *  @example
 *    $('#example').dataTable( {
 *       columnDefs: [
 *         { type: 'title-string', targets: 0 }
 *       ]
 *    } );
 */

jQuery.extend( jQuery.fn.dataTableExt.oSort, {
	"title-string-pre": function ( a ) {
		return a.match(/title="(.*?)"/)[1].toLowerCase();
	},

	"title-string-asc": function ( a, b ) {
		return ((a < b) ? -1 : ((a > b) ? 1 : 0));
	},

	"title-string-desc": function ( a, b ) {
		return ((a < b) ? 1 : ((a > b) ? -1 : 0));
	}
} );
