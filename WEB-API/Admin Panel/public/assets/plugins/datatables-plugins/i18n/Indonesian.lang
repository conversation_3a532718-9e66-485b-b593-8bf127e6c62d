/**
 * Indonesian translation
 *  @name Indonesian
 *  @anchor Indonesian
 *  <AUTHOR>
 */

{
	"sProcessing":   "Sedang memproses...",
	"sLengthMenu":   "<PERSON><PERSON><PERSON>an _MENU_ entri",
	"sZeroRecords":  "Tidak ditemukan data yang sesuai",
	"sInfo":         "Menampilkan _START_ sampai _END_ dari _TOTAL_ entri",
	"sInfoEmpty":    "Menampilkan 0 sampai 0 dari 0 entri",
	"sInfoFiltered": "(disaring dari _MAX_ entri kese<PERSON>han)",
	"sInfoPostFix":  "",
	"sSearch":       "Cari:",
	"sUrl":          "",
	"oPaginate": {
		"sFirst":    "Pertama",
		"sPrevious": "Sebelumnya",
		"sNext":     "Selanjutnya",
		"sLast":     "Terakhir"
	}
}