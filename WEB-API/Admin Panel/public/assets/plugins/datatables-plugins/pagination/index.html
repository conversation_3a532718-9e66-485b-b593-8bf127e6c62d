
<h2>Custom pagination types</h2>

<p>The style of the pagination options that Datatables presents to the end-user can greatly effect the look and feel of your table, as well as, of course, the interaction behaviour. Through the plug-in options you can define your own paging function to create the interaction that you are looking for.</p>

<ul>
	<li><a href="#how_to">How to use DataTables plug-in pagination functions</a></li>
	<li><a href="#functions">Plug-in pagination functions</a></li>
</ul>


<a name="how_to"></a>
<h3>How to use DataTables plug-in pagination functions</h3>

<p>To use a pagination plug-in, you must include the pagination plug-in code from the plug-ins available below, after you load the DataTables library, but before you initialise the DataTable. When initialising the DataTable, you must also tell it to make use of this plug-in, rather than using the default built-in types, by setting the 'sPaginationType' to the value required by the plug-in. As an example the code below makes use of the <a href="#scrolling">scrolling pagination plug-in</a> saved into a file (<a href="/examples/plug-ins/paging_plugin.html">live example</a>):</p>

<pre class="brush: html">&lt;script type="text/javascript" src="jquery.dataTables.js"&gt;&lt;/script&gt;
&lt;script type="text/javascript" src="dataTables.scrollingPagination.js"&gt;&lt;/script&gt;
&lt;script type="text/javascript"&gt;
	$(document).ready(function() {
		$('#example').dataTable( {
			"sPaginationType": "scrolling"
		} );
	} );
&lt;/script&gt;
</pre>

				
<a name="functions"></a>
<h3>Plug-in pagination functions</h3>


include(`build.1.inc')
