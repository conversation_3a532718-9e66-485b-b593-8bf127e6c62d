/**
 * Redraw the table (i.e. `fnDraw`) to take account of sorting and filtering,
 * but retain the current pagination settings.
 *
 * DataTables 1.10+ provide the `dt-api draw()` method which has this ability
 * built-in (pass the first parameter to the function as `false`). As such this
 * method is marked deprecated, but is available for use with legacy version of
 * DataTables. Please use the new API if you are used DataTables 1.10 or newer.
 *
 *  @name fnStandingRedraw
 *  @summary Redraw the table without altering the paging
 *  <AUTHOR>
 *  @deprecated
 *
 *  @example
 *    $(document).ready(function() {
 *        var table = $('.dataTable').dataTable()
 *        table.fnStandingRedraw();
 *    } );
 */

jQuery.fn.dataTableExt.oApi.fnStandingRedraw = function(oSettings) {
    if(oSettings.oFeatures.bServerSide === false){
        var before = oSettings._iDisplayStart;

        oSettings.oApi._fnReDraw(oSettings);

        // iDisplayStart has been reset to zero - so lets change it back
        oSettings._iDisplayStart = before;
        oSettings.oApi._fnCalculateEnd(oSettings);
    }

    // draw the 'current' page
    oSettings.oApi._fnDraw(oSettings);
};
