/**
 * Telephone numbers are a common data point to display in HTML tables, and are
 * often formatted (e.g. `dt-string 555-1234`). Typically, when searching a
 * table a user would need to enter the number in exactly the same format it is
 * displayed in, but this is not always convenient (e.g. you might search for
 * `dt-string 5551`).
 *
 * This filtering plug-in will allow both forms to be matched be providing both
 * the formatted and de-formatted data to the table's search.
 * 
 *  @summary Make phone numbers searchable formatted or unformatted
 *  @name Phone number
 *  <AUTHOR>
 *
 *  @example
 *    $(document).ready(function() {
 *      $('#example').dataTable( {
 *        columnDefs: [
 *          { type: 'phoneNumber', target: 4 }
 *        ]
 *      } );
 *    } );
 */

jQuery.fn.DataTable.ext.type.search.phoneNumber = function ( data ) {
    return ! data ?
        '' :
        typeof data === 'string' ?
            data + data.replace(/[ \-]/g, '') :
            data;
};
