# <pre>
# This file is in the public domain, so clarified as of
# 2009-05-17 by <PERSON>.

# These entries are mostly present for historical reasons, so that
# people in areas not otherwise covered by the tz files could "zic -l"
# to a time zone that was right for their area.  These days, the
# tz files cover almost all the inhabited world, and the only practical
# need now for the entries that are not on UTC are for ships at sea
# that cannot use POSIX TZ settings.

Zone	Etc/GMT		0	-	GMT
Zone	Etc/UTC		0	-	UTC
Zone	Etc/UCT		0	-	UCT

# The following link uses older naming conventions,
# but it belongs here, not in the file `backward',
# as functions like gmtime load the "GMT" file to handle leap seconds properly.
# We want this to work even on installations that omit the other older names.
Link	Etc/GMT				GMT

Link	Etc/UTC				Etc/Universal
Link	Etc/UTC				Etc/Zulu

Link	Etc/GMT				Etc/Greenwich
Link	Etc/GMT				Etc/GMT-0
Link	Etc/GMT				Etc/GMT+0
Link	Etc/GMT				Etc/GMT0

# We use POSIX-style signs in the Zone names and the output abbreviations,
# even though this is the opposite of what many people expect.
# POSIX has positive signs west of Greenwich, but many people expect
# positive signs east of Greenwich.  For example, TZ='Etc/GMT+4' uses
# the abbreviation "GMT+4" and corresponds to 4 hours behind UTC
# (i.e. west of Greenwich) even though many people would expect it to
# mean 4 hours ahead of UTC (i.e. east of Greenwich).
#
# In the draft 5 of POSIX 1003.1-200x, the angle bracket notation allows for
# TZ='<GMT-4>+4'; if you want time zone abbreviations conforming to
# ISO 8601 you can use TZ='<-0400>+4'.  Thus the commonly-expected
# offset is kept within the angle bracket (and is used for display)
# while the POSIX sign is kept outside the angle bracket (and is used
# for calculation).
#
# Do not use a TZ setting like TZ='GMT+4', which is four hours behind
# GMT but uses the completely misleading abbreviation "GMT".

# Earlier incarnations of this package were not POSIX-compliant,
# and had lines such as
#		Zone	GMT-12		-12	-	GMT-1200
# We did not want things to change quietly if someone accustomed to the old
# way does a
#		zic -l GMT-12
# so we moved the names into the Etc subdirectory.

Zone	Etc/GMT-14	14	-	GMT-14	# 14 hours ahead of GMT
Zone	Etc/GMT-13	13	-	GMT-13
Zone	Etc/GMT-12	12	-	GMT-12
Zone	Etc/GMT-11	11	-	GMT-11
Zone	Etc/GMT-10	10	-	GMT-10
Zone	Etc/GMT-9	9	-	GMT-9
Zone	Etc/GMT-8	8	-	GMT-8
Zone	Etc/GMT-7	7	-	GMT-7
Zone	Etc/GMT-6	6	-	GMT-6
Zone	Etc/GMT-5	5	-	GMT-5
Zone	Etc/GMT-4	4	-	GMT-4
Zone	Etc/GMT-3	3	-	GMT-3
Zone	Etc/GMT-2	2	-	GMT-2
Zone	Etc/GMT-1	1	-	GMT-1
Zone	Etc/GMT+1	-1	-	GMT+1
Zone	Etc/GMT+2	-2	-	GMT+2
Zone	Etc/GMT+3	-3	-	GMT+3
Zone	Etc/GMT+4	-4	-	GMT+4
Zone	Etc/GMT+5	-5	-	GMT+5
Zone	Etc/GMT+6	-6	-	GMT+6
Zone	Etc/GMT+7	-7	-	GMT+7
Zone	Etc/GMT+8	-8	-	GMT+8
Zone	Etc/GMT+9	-9	-	GMT+9
Zone	Etc/GMT+10	-10	-	GMT+10
Zone	Etc/GMT+11	-11	-	GMT+11
Zone	Etc/GMT+12	-12	-	GMT+12
