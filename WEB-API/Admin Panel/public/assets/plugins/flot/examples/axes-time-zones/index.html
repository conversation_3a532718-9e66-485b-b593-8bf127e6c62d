<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
	<title>Flot Examples: Time zones</title>
	<link href="../examples.css" rel="stylesheet" type="text/css">
	<!--[if lte IE 8]><script language="javascript" type="text/javascript" src="../../excanvas.min.js"></script><![endif]-->
	<script language="javascript" type="text/javascript" src="../../jquery.js"></script>
	<script language="javascript" type="text/javascript" src="../../jquery.flot.js"></script>
	<script language="javascript" type="text/javascript" src="../../jquery.flot.time.js"></script>
	<script language="javascript" type="text/javascript" src="date.js"></script>
	<script type="text/javascript">

	$(function() {

		timezoneJS.timezone.zoneFileBasePath = "tz";
		timezoneJS.timezone.defaultZoneFile = [];
		timezoneJS.timezone.init({async: false});

		var d = [
			[Date.UTC(2011, 2, 12, 14, 0, 0), 28],
			[Date.UTC(2011, 2, 12, 15, 0, 0), 27],
			[Date.UTC(2011, 2, 12, 16, 0, 0), 25],
			[Date.UTC(2011, 2, 12, 17, 0, 0), 19],
			[Date.UTC(2011, 2, 12, 18, 0, 0), 16],
			[Date.UTC(2011, 2, 12, 19, 0, 0), 14],
			[Date.UTC(2011, 2, 12, 20, 0, 0), 11],
			[Date.UTC(2011, 2, 12, 21, 0, 0), 9],
			[Date.UTC(2011, 2, 12, 22, 0, 0), 7.5],
			[Date.UTC(2011, 2, 12, 23, 0, 0), 6],
			[Date.UTC(2011, 2, 13, 0, 0, 0), 5],
			[Date.UTC(2011, 2, 13, 1, 0, 0), 6],
			[Date.UTC(2011, 2, 13, 2, 0, 0), 7.5],
			[Date.UTC(2011, 2, 13, 3, 0, 0), 9],
			[Date.UTC(2011, 2, 13, 4, 0, 0), 11],
			[Date.UTC(2011, 2, 13, 5, 0, 0), 14],
			[Date.UTC(2011, 2, 13, 6, 0, 0), 16],
			[Date.UTC(2011, 2, 13, 7, 0, 0), 19],
			[Date.UTC(2011, 2, 13, 8, 0, 0), 25],
			[Date.UTC(2011, 2, 13, 9, 0, 0), 27],
			[Date.UTC(2011, 2, 13, 10, 0, 0), 28],
			[Date.UTC(2011, 2, 13, 11, 0, 0), 29],
			[Date.UTC(2011, 2, 13, 12, 0, 0), 29.5],
			[Date.UTC(2011, 2, 13, 13, 0, 0), 29],
			[Date.UTC(2011, 2, 13, 14, 0, 0), 28],
			[Date.UTC(2011, 2, 13, 15, 0, 0), 27],
			[Date.UTC(2011, 2, 13, 16, 0, 0), 25],
			[Date.UTC(2011, 2, 13, 17, 0, 0), 19],
			[Date.UTC(2011, 2, 13, 18, 0, 0), 16],
			[Date.UTC(2011, 2, 13, 19, 0, 0), 14],
			[Date.UTC(2011, 2, 13, 20, 0, 0), 11],
			[Date.UTC(2011, 2, 13, 21, 0, 0), 9],
			[Date.UTC(2011, 2, 13, 22, 0, 0), 7.5],
			[Date.UTC(2011, 2, 13, 23, 0, 0), 6]
		];

		var plot = $.plot("#placeholderUTC", [d], {
			xaxis: {
				mode: "time"
			}
		});

		var plot = $.plot("#placeholderLocal", [d], {
			xaxis: {
				mode: "time",
				timezone: "browser"
			}
		});

		var plot = $.plot("#placeholderChicago", [d], {
			xaxis: {
				mode: "time",
				timezone: "America/Chicago"
			}
		});

		// Add the Flot version string to the footer

		$("#footer").prepend("Flot " + $.plot.version + " &ndash; ");
	});

	</script>
</head>
<body>

	<div id="header">
		<h2>Time zones</h2>
	</div>

	<div id="content">

		<h3>UTC</h3>
		<div class="demo-container" style="height: 300px;">
			<div id="placeholderUTC" class="demo-placeholder"></div>
		</div>

		<h3>Browser</h3>
		<div class="demo-container" style="height: 300px;">
			<div id="placeholderLocal" class="demo-placeholder"></div>
		</div>

		<h3>Chicago</h3>
		<div class="demo-container" style="height: 300px;">
			<div id="placeholderChicago" class="demo-placeholder"></div>
		</div>

	</div>

	<div id="footer">
		Copyright &copy; 2007 - 2014 IOLA and Ole Laursen
	</div>

</body>
</html>
