<?xml version="1.0" encoding="UTF-8" ?>
<dt-example table-type="html-wide" table-class="display dt-responsive nowrap" order="1" framework="bootstrap">

<css lib="datatables responsive"/>
<js lib="jquery datatables responsive">
<![CDATA[

$(document).ready(function() {
	$('#example').DataTable();
} );

]]>
</js>

<title lib="Responsive">Bootstrap styling</title>

<info><![CDATA[

This example shows DataTables and the Responsive extension being used with the [Bootstrap](http://getbootstrap.com) framework providing the styling. The [DataTables / Bootstrap integration](//datatables.net/manual/styling/bootstrap) prove seamless integration for DataTables to be used in a Bootstrap page.

Note that the `dt-responsive` class is used to indicate to the extension that it should be enabled on this page, as `responsive` [has special meaning in Bootstrap](http://getbootstrap.com/css/#tables-responsive). The `r-init responsive` option could also be used if required.

]]></info>

</dt-example>

