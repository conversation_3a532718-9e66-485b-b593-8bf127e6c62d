<?xml version="1.0" encoding="UTF-8" ?>
<dt-example order="0">

<title lib="Responsive">Child row control</title>

<info><![CDATA[

When a column is removed from display by Responsive, the data is still available in the table and can be displayed in a DataTables _child row_ (see `dt-api row().child()`). By default Responsive will show child row controls in the first column when the table has been collapsed, allowing the end user to show / hide the information from the hidden columns.

Responsive has a number of options for display of the child rows:

* If child row display is enabled: `r-init responsive.details`
* How the show / hide control is displayed: `r-init responsive.details.type`
* How the child row is rendered: `r-init responsive.details.renderer`

This section shows examples of these options being used.

]]></info>

</dt-example>
