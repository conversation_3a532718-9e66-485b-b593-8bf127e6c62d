<?xml version="1.0" encoding="UTF-8" ?>
<dt-example table-type="html-details-right" table-class="display nowrap" order="3">

<css lib="datatables responsive" />
<js lib="jquery datatables responsive">
<![CDATA[

$(document).ready(function() {
	$('#example').DataTable( {
		responsive: {
			details: {
				type: 'column',
				target: -1
			}
		},
		columnDefs: [ {
			className: 'control',
			orderable: false,
			targets:   -1
		} ]
	} );
} );

]]>
</js>

<title lib="Responsive">Column control - right</title>

<info><![CDATA[

When using the `column` child row control type, Responsive has the ability to use any column or element as the show / hide control for the row details. This is provided through the `r-init responsive.details.target` option, which can be either a column index, or a jQuery selector.

This example shows the last column in the table being used as the control column.

]]></info>

</dt-example>

