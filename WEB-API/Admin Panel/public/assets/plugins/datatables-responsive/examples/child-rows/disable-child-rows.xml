<?xml version="1.0" encoding="UTF-8" ?>
<dt-example table-type="html-wide" table-class="display nowrap" order="1">

<css lib="datatables responsive" />
<js lib="jquery datatables responsive">
<![CDATA[

$(document).ready(function() {
	$('#example').DataTable( {
		responsive: {
			details: false
		}
	} );
} );

]]>
</js>

<title lib="Responsive">Disable child rows</title>

<info><![CDATA[

By default, when Responsive collapses a table, it will show an option for the end user to expand the row, showing the details of the hidden columns in a child row. This can be disabled using the `r-init responsive.details` option and setting it to `false`, as shown in the example below. In this case the hidden data is not directly accessible to the end user.

]]></info>

</dt-example>

