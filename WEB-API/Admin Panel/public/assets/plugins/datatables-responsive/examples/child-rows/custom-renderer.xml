<?xml version="1.0" encoding="UTF-8" ?>
<dt-example table-type="html-wide" table-class="display nowrap" order="5">

<css lib="datatables responsive" />
<js lib="jquery datatables responsive">
<![CDATA[

$(document).ready(function() {
	$('#example').DataTable( {
		responsive: {
			details: {
				renderer: function ( api, rowIdx ) {
					// Select hidden columns for the given row
					var data = api.cells( rowIdx, ':hidden' ).eq(0).map( function ( cell ) {
						var header = $( api.column( cell.column ).header() );

						return '<tr>'+
								'<td>'+
									header.text()+':'+
								'</td> '+
								'<td>'+
									api.cell( cell ).data()+
								'</td>'+
							'</tr>';
					} ).toArray().join('');

					return data ?
						$('<table/>').append( data ) :
						false;
				}
			}
		}
	} );
} );

]]>
</js>

<title lib="Responsive">Custom child row renderer</title>

<info><![CDATA[

The child row's for a collapsed table in Responsive, by default, show a `-tag ul/li` list of the data from the hidden columns. The `r-init responsive.details.renderer` option provide the ability to create your own custom renderer. It is given two parameters: the DataTables API instance for the table and the row index to use.

This example shows the `dt-api cells()` method being used to select the hidden columns and constructing a table of the data. You could refine the selector to select only certain columns, or show all columns, etc.

]]></info>

</dt-example>

