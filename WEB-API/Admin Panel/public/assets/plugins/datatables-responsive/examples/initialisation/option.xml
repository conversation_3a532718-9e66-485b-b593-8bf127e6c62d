<?xml version="1.0" encoding="UTF-8" ?>
<dt-example table-type="html-wide" table-class="display nowrap" order="2">

<css lib="datatables responsive">
	div.container { max-width: 1200px }
</css>
<js lib="jquery datatables responsive">
<![CDATA[

$(document).ready(function() {
	$('#example').DataTable( {
		responsive: true
	} );
} );

]]>
</js>

<title lib="Responsive">Configuration option</title>

<info><![CDATA[

The Responsive extension for DataTables can be applied to a DataTable in one of two ways; with a specific class name on the table, or using the DataTables initialisation options. This method shows the latter, with the `r-init responsive` option being set to the boolean value `true`.

The `r-init responsive` option can be given as a boolean value, or as an object with configuration options. If as a boolean, as in this case, the default options are used.

]]></info>

</dt-example>

