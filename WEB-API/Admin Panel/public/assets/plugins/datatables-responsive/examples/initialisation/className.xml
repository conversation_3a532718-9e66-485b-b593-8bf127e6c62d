<?xml version="1.0" encoding="UTF-8" ?>
<dt-example table-type="html-wide" table-class="display responsive nowrap" order="1">

<css lib="datatables responsive" />
<js lib="jquery datatables responsive">
<![CDATA[

$(document).ready(function() {
	$('#example').DataTable();
} );

]]>
</js>

<title lib="Responsive">Class name</title>

<info><![CDATA[

The easiest way to initialise the Responsive extension for DataTables is simply to add the class `-string responsive` to the table's class name. When the DataTable is initialised the Responsive extension will automatically enable itself on these tables.

The may also use the class `dt-responsive` to perform the same action, since `responsive` may be used in your stylesheet, or may have some other meaning in a CSS framework being used (for example Bootstrap).

]]></info>

</dt-example>

