<?xml version="1.0" encoding="UTF-8" ?>
<dt-example table-type="ajax" table-class="display responsive nowrap" order="4">

<css lib="datatables responsive">
	div.container { max-width: 1200px }
</css>
<js lib="jquery datatables responsive">
<![CDATA[

$(document).ready(function() {
	$('#example').DataTable( {
		"ajax": "../../../../examples/ajax/data/objects.txt",
		"columns": [
			{ "data": "name" },
			{ "data": "position" },
			{ "data": "office" },
			{ "data": "extn" },
			{ "data": "start_date" },
			{ "data": "salary" }
		]
	} );
} );

]]>
</js>

<title lib="Responsive">Ajax data</title>

<info><![CDATA[

This example shows the Responsive extension working with [Ajax sourced data](//datatables.net/manual/data) in the DataTable. Note that no special initialisation is required. Responsive is enabled by adding the `-string responsive` class to the `-tag table` element.

]]></info>

</dt-example>

