<?xml version="1.0" encoding="UTF-8" ?>
<dt-example table-type="html-wide" table-class="display nowrap" order="5">

<css lib="datatables responsive" />
<js lib="jquery datatables responsive">
<![CDATA[

$.extend( $.fn.dataTable.defaults, {
    responsive: true
} );

$(document).ready(function() {
	$('#example').DataTable();
} );

]]>
</js>

<title lib="Responsive">Default initialisation</title>

<info><![CDATA[

It can often be useful to be able to set a default value for DataTables' initialisation options, providing a common starting point for initialisation when working with multiple tables over many pages or even just on a single page. DataTables provides that ability through the `$.fn.dataTable.defaults` object which can have any of the [initialisation options](//datatables.net/reference/option) set.

Extending that ability, Responsive can also be set to initialise by default, as shown in this example thorugh the `$.fn.dataTable.defaults.responsive` property. Extending that, [all of the Responsive options](//datatables.net/extensions/responsive/reference/option/) can also be set using this configuration option (i.e. use `responsive` as an object).

]]></info>

</dt-example>

