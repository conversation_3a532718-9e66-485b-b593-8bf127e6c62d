<?xml version="1.0" encoding="UTF-8" ?>
<dt-example order="0">

<title lib="Responsive">Display control</title>

<info><![CDATA[

Responsive has two basic modes of operation for controlling the visibility of columns at different display sizes. These two modes can be using either separately or together:

* Manually assigned class names for breakpoints - Assign a column a class name to tell Responsive which breakpoint(s) to show it in.
* Automatically - for columns without a breakpoint class name, it will be automatically removed if there is no room available on screen to show it. Columns are removed from the right, moving left.

This section explores these two options.

]]></info>

</dt-example>
