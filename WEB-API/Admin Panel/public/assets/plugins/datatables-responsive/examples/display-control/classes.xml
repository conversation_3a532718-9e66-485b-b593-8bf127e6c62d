<?xml version="1.0" encoding="UTF-8" ?>
<dt-example table-class="display responsive nowrap" order="2">

<css lib="datatables responsive">
</css>
<js lib="jquery datatables responsive">
<![CDATA[

$(document).ready(function() {
	$('#example').DataTable( {
		"ajax": "../../../../examples/ajax/data/objects.txt",
		"columns": [
			{ "data": "name" },
			{ "data": "position" },
			{ "data": "office" },
			{ "data": "start_date" },
			{ "data": "salary" },
			{ "data": "extn" }
		]
	} );
} );

]]>
</js>

<title lib="Responsive">Class control</title>

<info><![CDATA[

You can tell Responsive what columns to want to be visible on different devices through the use of class names on the columns. The breakpoints are horizontal screen resolutions and the defaults are set for common devices:

* `desktop` x >= 1024px
* `tablet-l` (landscape) 768 <= x < 1024
* `tablet-p` (portrait) 480 <= x < 768
* `mobile-l` (landscape) 320 <= x < 480
* `mobile-p` (portrait) x < 320

You may leave the `-[lp]` option from the end if you wish to just target all tablet or mobile devices. Additionally to may add `min-`, `max-` or `not-` as a prefix to the class name to perform logic operations. For example `not-mobile` would cause a column to appear as visible on desktop and tablet devices, while `min-tablet-l` would require at least a horizontal width of 768 for the browser window to be shown, and be shown at all sizes larger.

Additionally, there are three special class names:

* `all` - Always display
* `none` - Don't display as a column, but show in the child row
* `never` - Never display
* `control` - Used for the `column` `r-init responsive.details.type` option.

Please [refer to the Responsive manual](//datatables.net/extensions/responsive/) for further details of these options.

This example shows the `salary` column visible on a desktop only - `office` requires a tablet, while the `position` column requires a phone in landscape or larger. The `name` column is always visible and the `start date` is never visible.

This can be useful if you wish to change the format of the data shown on different devices, for example using a combination of `mobile` and `not-mobile` on two different columns would allow information to be formatted suitable for each device type.

]]></info>

<custom-table>
	<div id="breakpoint"> </div>
	<table id="example" class="display responsive" width="100%">
		<thead>
			<tr>
				<th class="all">Name</th>
				<th class="min-phone-l">Position</th>
				<th class="min-tablet">Office</th>
				<th class="never">Start date</th>
				<th class="desktop">Salary</th>
				<th class="none">Extn.</th>
			</tr>
		</thead>

		<tfoot>
			<tr>
				<th>Name</th>
				<th>Position</th>
				<th>Office</th>
				<th>Start date</th>
				<th>Salary</th>
				<th>Extn.</th>
			</tr>
		</tfoot>
	</table>
</custom-table>

</dt-example>

