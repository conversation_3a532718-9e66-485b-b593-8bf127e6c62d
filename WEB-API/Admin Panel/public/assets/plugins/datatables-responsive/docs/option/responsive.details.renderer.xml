<?xml version="1.0" encoding="UTF-8" ?>
<dt-option library="Responsive">
	<name>responsive.details.renderer</name>
	<summary>Define the renderer used to display the child rows</summary>
	<since>Responsive 1.0.0</since>

	<type type="function">
		<signature>renderer( api, rowIdx )</signature>
		<parameter type="DataTables.Api" name="api">
			DataTables API instance for the table in question
		</parameter>
		<parameter type="integer" name="rowIdx">
			Row index for the row that the renderer is being asked to render. Use the `dt-api row()` and / or `dt-api cells()` methods to get information from the API about the row so the information can be rendered.
		</parameter>
		<returns type="boolean|string">
			Two values can be returned:

			* `-type boolean` `false` - Do not display a child row
			* `-type string` - The information to be shown in the child row, including any required HTML.
		</returns>
	</type>

	<default value="function">
		Function that will display the hidden information in a `-tag ul/li` list.
	</default>

	<description>
		The information contained in the details rows that are displayed by Responsive are created through this function. By default it will create a `-tag ul/li` list showing the data from cells that are hidden, but you can provide a custom function that will create a child row containing whatever information you wish to display.

		This function is executed for every child row in a table, and is run whenever the column visibility of the table changes.

		Please note that as with all other configuration options for Responsive, this option is an extension to the [default set of DataTables options](/reference/option). This property should be set in the DataTables initialisation object.
	</description>

	<example title="Custom renderer which displays the data that has been hidden in an HTML table"><![CDATA[

$('#example').DataTable( {
	responsive: {
		details: {
			renderer: function ( api, rowIdx ) {
				// Select hidden columns for the given row
				var data = api.cells( rowIdx, ':hidden' ).eq(0).map( function ( cell ) {
					var header = $( api.column( cell.column ).header() );

					return '<tr>'+
							'<td>'+
								header.text()+':'+
							'</td> '+
							'<td>'+
								api.cell( cell ).data()+
							'</td>'+
						'</tr>';
				} ).toArray().join('');

				return data ?
					$('<table/>').append( data ) :
					false;
			}
		}
	}
} );

]]></example>

</dt-option>