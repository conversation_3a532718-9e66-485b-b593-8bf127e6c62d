<?xml version="1.0" encoding="UTF-8" ?>
<dt-option library="Responsive">
	<name>responsive.details.target</name>
	<summary>Column / selector for child row display control when using `column` details type</summary>
	<since>Responsive 1.0.0</since>

	<type type="number">
		<description>
			<![CDATA[
			Column index to which the show / hide control should be attached. This can be `>=0` to count columns from the left, or `<0` to count from the right.

			Please note that the column defined here should have the class `-string control` added to it. This allows the Responsive CSS to display a control icon to the end user.
			]]>
		</description>
	</type>

	<type type="string">
		<description>
			As a string, this option is used as a jQuery selector to determine what element(s) will activate the show / hide control for the details child rows. This provides the ability to use any element in a table - for example you can use the whole row, or a single `-tag img` element in the row.
		</description>
	</type>

	<default value="0"/>

	<description>
		When the `column` type is selected for the `r-init responsive.details.type` option, this option provides the ability to control what element in the table will activate the show / hide control in each row. This can be one of an column index, or a jQuery selector, as detailed below.

		If the `inline` option is used for `r-init responsive.details.type` this option has no effect.

		Please note that as with all other configuration options for Responsive, this option is an extension to the [default set of DataTables options](/reference/option). This property should be set in the DataTables initialisation object.
	</description>

	<example title="Use the `column` control type and target the right most column"><![CDATA[

$('#example').DataTable( {
	responsive: {
		details: {
			type: 'column',
			target: -1
		}
	},
	columnDefs: [ {
		className: 'control',
		orderable: false,
		targets:   -1
	} ]
} );

]]></example>

	<example title="Use the whole row to show / hide the details row"><![CDATA[

$('#example').DataTable( {
	responsive: {
		details: {
			type: 'column',
			target: 'tr'
		}
	}
} );

]]></example>

</dt-option>