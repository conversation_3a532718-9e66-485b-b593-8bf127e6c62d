<?xml version="1.0" encoding="UTF-8" ?>
<dt-api library="Responsive">
	<name>responsive.rebuild()</name>
	<summary>Recalculate the column breakpoints based on the class information of the column header cells</summary>
	<since>1.0.4</since>

	<type type="function">
		<signature>responsive.rebuild()</signature>
		<description>Recalculate the column breakpoints based on the class information of the column header cells</description>
		<returns type="DataTables.Api">DataTables API instance</returns>
	</type>

	<description>
		It can be useful to be able to control the column breakpoint information that Responsive responds to after a DataTable has been initialised. For example you may wish to provide user control over which columns are visible. This can be done using code that will add or remove the class `never` ([the other classes](http://datatables.net/extensions/responsive/classes) can also be used) and then calling this method.

		You will also likely wish to immediately call `r-api responsive.recalc()` to have Responsive update the display for the change in classes. Having this as a separate call allows multiple changes to be drawn at the same time to help improve performance.
	</description>

	<example title="Hide a column completely by adding a class name and rebuilding"><![CDATA[

var table = $('#example').DataTable();

$( table.column( 2 ).header() ).addClass( 'never' );

table.responsive.rebuild();
table.responsive.recalc();

]]></example>

</dt-api>