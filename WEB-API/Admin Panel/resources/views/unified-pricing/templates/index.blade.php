{{--
    صفحة قوالب التسعير الموحد (نسخة مُعادة الهيكلة)
    @version 4.0
--}}

@extends('layouts.app')

@push('styles')
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link href="{{ asset('css/select2.min.css') }}" rel="stylesheet">
    <link href="{{ asset('css/sweetalert2.min.css') }}" rel="stylesheet">
    <link href="{{ asset('css/unified-pricing-enhanced.css') }}" rel="stylesheet">
    <link href="{{ asset('css/unified-pricing/templates-table.css') }}" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" crossorigin="anonymous">
@endpush

@section('content')
<div class="page-wrapper">
    
    {{-- شريط العنوان والتنقل --}}
    <div class="row page-titles">
        <div class="col-md-5 align-self-center">
            <h3 class="text-themecolor">قوالب التسعير الموحد</h3>
        </div>
        <div class="col-md-7 align-self-center">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{url('/dashboard')}}">لوحة التحكم</a></li>
                <li class="breadcrumb-item"><a href="{{url('/unified-pricing')}}">التسعير الموحد</a></li>
                <li class="breadcrumb-item active">قوالب التسعير</li>
            </ol>
        </div>
    </div>

    <div class="container-fluid">
        
        {{-- قسم العنوان الرئيسي وزر الإنشاء --}}
        <div class="templates-header-section">
            <div class="row align-items-center">
                <div class="col-md-8">
                     {{-- ... (محتوى العنوان كما هو) --}}
                </div>
                <div class="col-md-4 text-end">
                    <button type="button" class="btn btn-primary btn-lg" id="create-template-btn-header">
                        <i class="fas fa-plus me-2"></i>إنشاء قالب جديد
                    </button>
                </div>
            </div>
        </div>

        {{-- (الإحصائيات السريعة كما هي) --}}

        {{-- حالة التحميل --}}
        <div id="loading-state" class="templates-loading-state" style="display: none;">
            {{-- ... (محتوى التحميل كما هو) --}}
        </div>

        {{-- حالة عدم وجود قوالب --}}
        <div id="empty-state" class="templates-empty-state" style="display: none;">
            <div class="templates-empty-icon"><i class="fas fa-layer-group"></i></div>
            <h4 class="empty-state-title">لا توجد قوالب تسعير</h4>
            <p class="empty-state-description">لم يتم إنشاء أي قوالب تسعير بعد.</p>
            <button type="button" class="btn btn-primary" id="create-template-btn-empty">
                <i class="fas fa-plus me-2"></i>إنشاء أول قالب
            </button>
        </div>

        {{-- أدوات البحث والفلترة --}}
        <div class="templates-filters-card card mb-4">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label for="search-input" class="form-label">البحث</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" id="search-input" placeholder="البحث في القوالب...">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label for="category-filter" class="form-label">الفئة</label>
                        <select class="form-select" id="category-filter">
                            <option value="">جميع الفئات</option>
                            <option value="popular">شائع</option>
                            <option value="recommended">موصى به</option>
                            <option value="custom">مخصص</option>
                            <option value="default">افتراضي</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="status-filter" class="form-label">الحالة</label>
                        <select class="form-select" id="status-filter">
                            <option value="">جميع الحالات</option>
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="sort-filter" class="form-label">الترتيب</label>
                        <select class="form-select" id="sort-filter">
                            <option value="name">الاسم</option>
                            <option value="created_at">تاريخ الإنشاء</option>
                            <option value="updated_at">آخر تحديث</option>
                        </select>
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="button" class="btn btn-outline-secondary w-100" id="reset-filters-btn">
                            <i class="fas fa-refresh me-1"></i>إعادة تعيين
                        </button>
                    </div>
                </div>
            </div>
        </div>

        {{-- تبويبات الفلترة السريعة --}}
        <div class="templates-tabs-card card mb-4">
            <div class="card-body">
                <ul class="nav nav-pills mb-0" id="templateTabs">
                    <li class="nav-item">
                        <a class="nav-link active" href="#" data-filter="all">
                            <i class="fas fa-th-large me-2"></i>جميع القوالب
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-filter="popular">
                            <i class="fas fa-fire me-2"></i>الأكثر استخداماً
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-filter="recommended">
                            <i class="fas fa-star me-2"></i>موصى به
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-filter="custom">
                            <i class="fas fa-user-cog me-2"></i>مخصص
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-filter="recent">
                            <i class="fas fa-clock me-2"></i>الأحدث
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        {{-- Enhanced Templates Table --}}
        <div class="card templates-table-wrapper" id="templates-table-card">
            <div class="table-responsive">
                <table class="table table-hover mb-0" id="templates-table">
                    <thead>
                        <tr>
                            <th width="40px"><input type="checkbox" id="select-all-checkbox"></th>
                            <th data-sort="name">@lang('name') <i class="fas fa-sort"></i></th>
                            <th data-sort="description">@lang('description')</th>
                            <th data-sort="status">@lang('status')</th>
                            <th data-sort="created_at">@lang('created at')</th>
                            <th data-sort="updated_at">@lang('updated at')</th>
                            <th width="200px">@lang('actions')</th>
                        </tr>
                    </thead>
                    <tbody id="templates-table-body">
                        @foreach($templates as $template)
                        @php
                            $id = is_object($template) ? $template->id : ($template['id'] ?? null);
                            $name = is_object($template) ? $template->name : ($template['name'] ?? '');
                            $desc = is_object($template) ? $template->description : ($template['description'] ?? '');
                            $active = is_object($template) ? $template->is_active : ($template['is_active'] ?? false);
                        @endphp
                        <tr data-template-id="{{ $id }}">
                            <td><input type="checkbox" class="template-checkbox"></td>
                            <td>{{ $name }}</td>
                            <td>{{ $desc }}</td>
                            <td>
                                <span class="badge bg-{{ $active ? 'success' : 'danger' }}">
                                    {{ $active ? __('active') : __('inactive') }}
                                </span>
                            </td>
                            <td>
                                @php
                                    $createdAt = is_object($template) ? $template->created_at : (isset($template['created_at'])
                                        ? (is_string($template['created_at']) ? \Illuminate\Support\Carbon::parse($template['created_at'])
                                        : $template['created_at']) : null);
                                @endphp
                                {{ optional($createdAt)->format('Y/m/d H:i') }}
                            </td>
                            <td>
                                @php
                                    $updatedAt = is_object($template) ? $template->updated_at : (isset($template['updated_at'])
                                        ? (is_string($template['updated_at']) ? \Illuminate\Support\Carbon::parse($template['updated_at'])
                                        : $template['updated_at']) : null);
                                @endphp
                                {{ optional($updatedAt)->format('Y/m/d H:i') }}
                            </td>
                            <td class="text-end">
                                <button class="btn btn-sm btn-outline-secondary preview-btn ms-1" data-id="{{ $id }}" title="@lang('Preview')">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-primary edit-btn ms-1" data-id="{{ $id }}" title="@lang('Edit')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-warning toggle-btn ms-1"
                                    data-id="{{ $id }}"
                                    data-status="{{ $active ? 1 : 0 }}"
                                    title="{{ $active ? __('Deactivate') : __('Activate') }}">
                                    <i class="fas {{ $active ? 'fa-toggle-on' : 'fa-toggle-off' }}"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger delete-btn ms-1" data-id="{{ $id }}" title="@lang('Delete')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            {{-- حالة عدم وجود نتائج بعد الفلترة --}}
            <div id="no-results-state" class="templates-empty-state" style="display: none;">
                {{-- ... (محتوى الحالة كما هو) --}}
            </div>
            
            {{-- أدوات الجدول السفلية --}}
            <div class="templates-table-footer">
                <div id="bulkActions" style="display: none;">
                    <button type="button" class="btn btn-sm btn-outline-success" id="bulk-activate-btn">تفعيل</button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="bulk-deactivate-btn">إلغاء تفعيل</button>
                    <button type="button" class="btn btn-sm btn-outline-danger" id="bulk-delete-btn">حذف</button>
                </div>
            </div>
        </div>

    </div>
</div>
@endsection

@push('scripts')
<!-- مكتبات أساسية من CDN للتأكد من التحميل -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<!-- ملفات نظام قوالب التسعير الموحد (مع fallback) -->
<script>
// التحقق من تحميل المكتبات الأساسية
if (typeof jQuery === 'undefined') {
    console.error('jQuery غير محمل!');
}
if (typeof Swal === 'undefined') {
    console.error('SweetAlert2 غير محمل!');
}
</script>

<script>
/**
 * إعدادات صفحة قوالب التسعير الموحد
 * تمرير البيانات من Laravel إلى JavaScript بشكل آمن ومنظم
 */

// تمرير بيانات القوالب من الخادم
window.templatesData = {
    templates: @json($templates ?? []),
    totalCount: {{ count($templates ?? []) }},
    activeCount: {{ collect($templates ?? [])->where('is_active', true)->count() }},
    categories: {
        popular: {{ collect($templates ?? [])->where('category', 'popular')->count() }},
        recommended: {{ collect($templates ?? [])->where('category', 'recommended')->count() }},
        custom: {{ collect($templates ?? [])->where('category', 'custom')->count() }},
        default: {{ collect($templates ?? [])->where('category', 'default')->count() }}
    }
};

// تمرير روابط النظام المخصصة لقوالب التسعير
window.templatesRoutes = {
    create: '{{ route("unified-pricing.templates.create") }}',
    edit: '{{ url("/unified-pricing/templates") }}',
    show: '{{ url("/unified-pricing/templates") }}',
    delete: '{{ url("/unified-pricing/templates") }}',
    use: '{{ url("/unified-pricing/api/templates") }}',
    duplicate: '{{ url("/unified-pricing/templates") }}',
    export: '{{ url("/unified-pricing/templates") }}'
};

// تمرير معلومات المستخدم الحالي
window.currentUser = {
    id: {{ auth()->id() ?? 'null' }},
    name: '{{ auth()->user()->name ?? "مستخدم غير معروف" }}',
    role: '{{ auth()->user()->role ?? "user" }}',
    permissions: @json(auth()->user()->permissions ?? [])
};

// إعدادات النظام المخصصة لقوالب التسعير
window.templatesSystemSettings = {
    locale: '{{ app()->getLocale() }}',
    timezone: '{{ config("app.timezone") }}',
    currency: '{{ config("app.currency", "SAR") }}',
    dateFormat: 'YYYY-MM-DD',
    timeFormat: 'HH:mm:ss',
    pagination: {
        perPage: 10,
        maxPerPage: 50
    },
    validation: {
        maxNameLength: 100,
        maxDescriptionLength: 500,
        minRating: 0,
        maxRating: 5
    }
};

// رسائل النظام المخصصة لقوالب التسعير
window.templatesMessages = {
    success: {
        created: 'تم إنشاء القالب بنجاح',
        updated: 'تم تحديث القالب بنجاح',
        deleted: 'تم حذف القالب بنجاح',
        activated: 'تم تفعيل القالب بنجاح',
        deactivated: 'تم إلغاء تفعيل القالب بنجاح',
        used: 'تم تطبيق القالب بنجاح'
    },
    error: {
        notFound: 'لم يتم العثور على القالب المطلوب',
        loadFailed: 'فشل في تحميل القوالب',
        saveFailed: 'فشل في حفظ القالب',
        deleteFailed: 'فشل في حذف القالب',
        updateFailed: 'فشل في تحديث القالب',
        networkError: 'خطأ في الاتصال بالخادم',
        invalidData: 'البيانات المدخلة غير صحيحة'
    },
    confirm: {
        delete: 'هل أنت متأكد من حذف هذا القالب؟ لا يمكن التراجع عن هذا الإجراء.',
        bulkDelete: 'هل أنت متأكد من حذف القوالب المحددة؟ لا يمكن التراجع عن هذا الإجراء.',
        bulkActivate: 'هل تريد تفعيل جميع القوالب المحددة؟',
        bulkDeactivate: 'هل تريد إلغاء تفعيل جميع القوالب المحددة؟',
        use: 'هل تريد استخدام هذا القالب لإنشاء إعداد تسعير جديد؟'
    },
    info: {
        selectTemplates: 'يرجى تحديد قالب واحد على الأقل',
        noResults: 'لا توجد قوالب تطابق معايير البحث',
        loading: 'جاري تحميل القوالب...',
        processing: 'جاري المعالجة...'
    }
};

// رسائل النظام المخصصة لقوالب التسعير
window.templatesMessages = {
    // رسائل النجاح
    success: {
        created: 'تم إنشاء قالب التسعير بنجاح',
        updated: 'تم تحديث قالب التسعير بنجاح',
        deleted: 'تم حذف قالب التسعير بنجاح',
        used: 'تم تطبيق قالب التسعير بنجاح',
        activated: 'تم تفعيل القالب بنجاح',
        deactivated: 'تم إلغاء تفعيل القالب بنجاح'
    },

    // رسائل التأكيد
    confirm: {
        delete: 'هل أنت متأكد من حذف قالب التسعير؟ لا يمكن التراجع عن هذا الإجراء.',
        use: 'هل تريد تطبيق هذا القالب؟ سيتم استخدام إعداداته في النظام.',
        bulkDelete: 'هل أنت متأكد من حذف القوالب المحددة؟',
        bulkActivate: 'هل تريد تفعيل جميع القوالب المحددة؟',
        bulkDeactivate: 'هل تريد إلغاء تفعيل جميع القوالب المحددة؟'
    },

    // رسائل الأخطاء
    error: {
        general: 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.',
        notFound: 'لم يتم العثور على قالب التسعير المطلوب',
        network: 'خطأ في الاتصال بالخادم. تحقق من اتصالك بالإنترنت.',
        validation: 'يرجى التحقق من البيانات المدخلة وإصلاح الأخطاء.',
        permission: 'ليس لديك صلاحية لتنفيذ هذا الإجراء.',
        inUse: 'لا يمكن حذف هذا القالب لأنه قيد الاستخدام حالياً.'
    },

    // رسائل المعلومات
    info: {
        loading: 'جاري تحميل قوالب التسعير...',
        noTemplates: 'لا توجد قوالب تسعير متاحة',
        noResults: 'لم يتم العثور على نتائج تطابق البحث',
        selectTemplates: 'يرجى تحديد قالب واحد على الأقل'
    }
};

// تسجيل معلومات التهيئة في وحدة التحكم
console.log('🎯 تم تهيئة صفحة قوالب التسعير الموحد:');
console.log('📊 عدد القوالب:', window.templatesData.totalCount);
console.log('✅ القوالب النشطة:', window.templatesData.activeCount);
console.log('👤 المستخدم الحالي:', window.currentUser.name);
console.log('🌐 اللغة:', window.templatesSystemSettings.locale);
console.log('💰 العملة:', window.templatesSystemSettings.currency);

/**
 * وظائف مخصصة لإدارة قوالب التسعير
 * تركز حصرياً على وظائف القوالب دون تداخل مع أنظمة أخرى
 */

// وظيفة إنشاء قالب تسعير جديد
function handleCreateTemplate() {
    console.log('🆕 بدء إنشاء قالب تسعير جديد');

    if (typeof Swal !== 'undefined') {
        Swal.fire({
            title: 'إنشاء قالب تسعير جديد',
            text: 'سيتم توجيهك إلى صفحة إنشاء قالب تسعير جديد',
            icon: 'info',
            showCancelButton: true,
            confirmButtonText: 'متابعة الإنشاء',
            cancelButtonText: 'إلغاء',
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#6c757d'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = window.templatesRoutes.create;
            }
        });
    } else {
        if (confirm('هل تريد إنشاء قالب تسعير جديد؟')) {
            window.location.href = window.templatesRoutes.create;
        }
    }
}

// وظيفة معاينة قالب التسعير
function showTemplatePreview(templateId) {
    try {
        console.log('👁️ معاينة قالب التسعير:', templateId);

        if (!templateId) {
            console.error('❌ معرف القالب مطلوب للمعاينة');
            showError('معرف القالب غير صحيح');
            return;
        }

        const template = window.templatesData.templates.find(t => t.id == templateId);

        if (!template) {
            console.error(`❌ لم يتم العثور على القالب: ${templateId}`);
            showError('لم يتم العثور على القالب المطلوب');
            return;
        }

        console.log('✅ تم العثور على القالب للمعاينة:', template);

        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'معاينة قالب التسعير',
                html: generateTemplatePreviewHTML(template),
                showCancelButton: true,
                confirmButtonText: 'استخدام القالب',
                cancelButtonText: 'إغلاق',
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d',
                width: '700px',
                customClass: {
                    htmlContainer: 'text-start'
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    useTemplate(templateId);
                }
            });
        } else {
            console.warn('⚠️ SweetAlert غير متوفر، استخدام alert عادي');
            alert(`معاينة القالب: ${template.name}\n\nالوصف: ${template.description || 'غير محدد'}`);
        }
    } catch (error) {
        console.error('❌ خطأ في معاينة القالب:', error);
        showError('حدث خطأ أثناء معاينة القالب');
    }
}

// وظيفة إنشاء HTML لمعاينة القالب
function generateTemplatePreviewHTML(template) {
    const statusNames = {
        'active': 'نشط',
        'inactive': 'غير نشط',
        'draft': 'مسودة'
    };

    return `
        <div class="row g-3">
            <div class="col-md-6">
                <h6 class="text-primary mb-3">
                    <i class="fas fa-info-circle me-2"></i>المعلومات الأساسية
                </h6>
                <p><strong>اسم القالب:</strong> ${template.name || 'غير محدد'}</p>
                <p><strong>الوصف:</strong> ${template.description || 'لا يوجد وصف'}</p>
                <p><strong>الحالة:</strong> ${template.is_active ? 'نشط' : 'غير نشط'}</p>
            </div>
            <div class="col-md-6">
                <h6 class="text-primary mb-3">
                    <i class="fas fa-chart-bar me-2"></i>الإحصائيات
                </h6>
                <p><strong>تاريخ الإنشاء:</strong> ${template.created_at || 'غير محدد'}</p>
                <p><strong>آخر تحديث:</strong> ${template.updated_at || 'غير محدد'}</p>
            </div>
        </div>
    `;
}

// وظيفة استخدام قالب التسعير
function useTemplate(templateId) {
    try {
        console.log('✅ استخدام قالب التسعير:', templateId);

        if (!templateId) {
            console.error('❌ معرف القالب مطلوب للاستخدام');
            showError('معرف القالب غير صحيح');
            return;
        }

        const template = window.templatesData.templates.find(t => t.id == templateId);

        if (!template) {
            console.error(`❌ لم يتم العثور على القالب: ${templateId}`);
            showError('لم يتم العثور على القالب المطلوب');
            return;
        }

        console.log('✅ تم العثور على القالب للاستخدام:', template);

        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'استخدام قالب التسعير؟',
                text: `سيتم تطبيق إعدادات قالب "${template.name}" في النظام`,
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'نعم، استخدم القالب',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d'
            }).then((result) => {
                if (result.isConfirmed) {
                    // إرسال طلب AJAX لاستخدام القالب
                    useTemplateAjax(templateId, template);
                }
            });
        } else {
            console.warn('⚠️ SweetAlert غير متوفر، استخدام confirm عادي');
            if (confirm(`هل تريد استخدام قالب "${template.name}"؟`)) {
                useTemplateAjax(templateId, template);
            }
        }
    } catch (error) {
        console.error('❌ خطأ في استخدام القالب:', error);
        showError('حدث خطأ أثناء استخدام القالب');
    }
}

// وظيفة إرسال طلب AJAX لاستخدام القالب
function useTemplateAjax(templateId, template) {
    try {
        console.log('📡 إرسال طلب استخدام القالب:', templateId);

        // عرض مؤشر التحميل
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'جاري تطبيق القالب...',
                text: 'يرجى الانتظار',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
        }

        // إرسال الطلب إلى الخادم
        fetch(`${window.templatesRoutes.use}/${templateId}/use`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            },
            body: JSON.stringify({
                name: `${template.name} - نسخة`,
                display_name: `${template.name} - نسخة`,
                description: `تم إنشاؤه من القالب: ${template.name}`,
                scope: template.scope || 'global'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('✅ تم تطبيق القالب بنجاح:', data);
                showSuccess(data.message || 'تم تطبيق القالب بنجاح');

                // التوجه إلى الصفحة المحددة إذا كانت متوفرة
                if (data.redirect) {
                    setTimeout(() => {
                        window.location.href = data.redirect;
                    }, 1500);
                }
            } else {
                console.error('❌ فشل في تطبيق القالب:', data);
                showError(data.message || 'فشل في تطبيق القالب');
            }
        })
        .catch(error => {
            console.error('❌ خطأ في طلب استخدام القالب:', error);
            showError('حدث خطأ أثناء تطبيق القالب');
        });
    } catch (error) {
        console.error('❌ خطأ في إرسال طلب استخدام القالب:', error);
        showError('حدث خطأ أثناء تطبيق القالب');
    }
}

// وظيفة تعديل قالب التسعير
function editTemplate(templateId) {
    try {
        console.log('✏️ تعديل قالب التسعير:', templateId);

        if (!templateId) {
            console.error('❌ معرف القالب مطلوب للتعديل');
            showError('معرف القالب غير صحيح');
            return;
        }

        const template = window.templatesData.templates.find(t => t.id == templateId);

        if (!template) {
            console.error(`❌ لم يتم العثور على القالب: ${templateId}`);
            showError('لم يتم العثور على القالب المطلوب');
            return;
        }

        console.log('✅ توجيه إلى صفحة تعديل القالب:', template);
        window.location.href = `${window.templatesRoutes.edit}/${templateId}/edit`;
    } catch (error) {
        console.error('❌ خطأ في تعديل القالب:', error);
        showError('حدث خطأ أثناء فتح صفحة التعديل');
    }
}

// وظيفة حذف قالب التسعير
function deleteTemplate(templateId) {
    try {
        console.log('🗑️ حذف قالب التسعير:', templateId);

        if (!templateId) {
            console.error('❌ معرف القالب مطلوب للحذف');
            showError('معرف القالب غير صحيح');
            return;
        }

        const template = window.templatesData.templates.find(t => t.id == templateId);

        if (!template) {
            console.error(`❌ لم يتم العثور على القالب: ${templateId}`);
            showError('لم يتم العثور على القالب المطلوب');
            return;
        }

        console.log('✅ تم العثور على القالب للحذف:', template);

        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'حذف قالب التسعير؟',
                text: `هل أنت متأكد من حذف قالب "${template.name}"؟ لا يمكن التراجع عن هذا الإجراء.`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'نعم، احذف القالب',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    performTemplateDelete(templateId, template.name);
                }
            });
        } else {
            console.warn('⚠️ SweetAlert غير متوفر، استخدام confirm عادي');
            if (confirm(`هل أنت متأكد من حذف قالب "${template.name}"؟`)) {
                performTemplateDelete(templateId, template.name);
            }
        }
    } catch (error) {
        console.error('❌ خطأ في حذف القالب:', error);
        showError('حدث خطأ أثناء حذف القالب');
    }
}

// تنفيذ عملية حذف القالب
function performTemplateDelete(templateId, templateName) {
    try {
        console.log('🗑️ تنفيذ حذف القالب:', templateId, templateName);

        // عرض مؤشر التحميل
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'جاري حذف القالب...',
                text: 'يرجى الانتظار',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
        }

        // إرسال طلب الحذف إلى الخادم
        fetch(`${window.templatesRoutes.delete}/${templateId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('✅ تم حذف القالب من الخادم بنجاح:', data);

                // إزالة الصف من الجدول مع تأثير بصري
                const row = document.querySelector(`tr[data-template-id="${templateId}"]`);
                if (row) {
                    row.style.transition = 'opacity 0.3s ease';
                    row.style.opacity = '0';

                    setTimeout(() => {
                        row.remove();
                        updateTableNumbers();
                        updateTableStats();

                        // التحقق من وجود قوالب متبقية
                        const remainingRows = document.querySelectorAll('#templates-table-body tr');
                        if (remainingRows.length === 0) {
                            showEmptyState();
                        }
                    }, 300);
                }

                // إزالة القالب من البيانات المحلية
                const templateIndex = window.templatesData.templates.findIndex(t => t.id == templateId);
                if (templateIndex !== -1) {
                    window.templatesData.templates.splice(templateIndex, 1);
                    window.templatesData.totalCount--;
                }

                // عرض رسالة نجاح
                showSuccess(data.message || `تم حذف قالب "${templateName}" بنجاح`);
            } else {
                console.error('❌ فشل في حذف القالب:', data);
                showError(data.message || 'فشل في حذف القالب');
            }
        })
        .catch(error => {
            console.error('❌ خطأ في طلب حذف القالب:', error);
            showError('حدث خطأ أثناء حذف القالب');
        });
    } catch (error) {
        console.error('❌ خطأ في تنفيذ حذف القالب:', error);
        showError('حدث خطأ أثناء حذف القالب');
    }
}

// وظيفة تغيير حالة القالب (تفعيل/إلغاء تفعيل)
function toggleTemplateStatus(templateId, currentStatus) {
    try {
        console.log('🔄 تغيير حالة القالب:', templateId, 'الحالة الحالية:', currentStatus);

        if (!templateId) {
            console.error('❌ معرف القالب مطلوب لتغيير الحالة');
            showError('معرف القالب غير صحيح');
            return;
        }

        const template = window.templatesData.templates.find(t => t.id == templateId);

        if (!template) {
            console.error(`❌ لم يتم العثور على القالب: ${templateId}`);
            showError('لم يتم العثور على القالب المطلوب');
            return;
        }

        const isCurrentlyActive = currentStatus == '1' || currentStatus === 'active';
        const newStatus = !isCurrentlyActive;
        const actionText = newStatus ? 'تفعيل' : 'إلغاء تفعيل';

        console.log('✅ تم العثور على القالب لتغيير الحالة:', template, 'الإجراء:', actionText);

        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: `${actionText} قالب التسعير؟`,
                text: `هل تريد ${actionText} قالب "${template.name}"؟`,
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: `نعم، ${actionText}`,
                cancelButtonText: 'إلغاء',
                confirmButtonColor: newStatus ? '#28a745' : '#ffc107',
                cancelButtonColor: '#6c757d'
            }).then((result) => {
                if (result.isConfirmed) {
                    performTemplateStatusToggle(templateId, newStatus, template.name);
                }
            });
        } else {
            console.warn('⚠️ SweetAlert غير متوفر، استخدام confirm عادي');
            if (confirm(`هل تريد ${actionText} قالب "${template.name}"؟`)) {
                performTemplateStatusToggle(templateId, newStatus, template.name);
            }
        }
    } catch (error) {
        console.error('❌ خطأ في تغيير حالة القالب:', error);
        showError('حدث خطأ أثناء تغيير حالة القالب');
    }
}

// تنفيذ عملية تغيير حالة القالب
function performTemplateStatusToggle(templateId, newStatus, templateName) {
    try {
        console.log('🔄 تنفيذ تغيير حالة القالب:', templateId, 'الحالة الجديدة:', newStatus);

        // عرض مؤشر التحميل
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'جاري تحديث الحالة...',
                text: 'يرجى الانتظار',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
        }

        // إرسال طلب تحديث الحالة إلى الخادم
        fetch(`${window.templatesRoutes.edit}/${templateId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            },
            body: JSON.stringify({
                is_active: newStatus
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('✅ تم تحديث حالة القالب بنجاح:', data);

                // تحديث الواجهة
                updateTemplateStatusInUI(templateId, newStatus);

                // تحديث البيانات المحلية
                const template = window.templatesData.templates.find(t => t.id == templateId);
                if (template) {
                    template.is_active = newStatus;
                }

                // عرض رسالة نجاح
                const statusText = newStatus ? 'تم تفعيل' : 'تم إلغاء تفعيل';
                showSuccess(data.message || `${statusText} قالب "${templateName}" بنجاح`);
            } else {
                console.error('❌ فشل في تحديث حالة القالب:', data);
                showError(data.message || 'فشل في تحديث حالة القالب');
            }
        })
        .catch(error => {
            console.error('❌ خطأ في طلب تحديث حالة القالب:', error);
            showError('حدث خطأ أثناء تحديث حالة القالب');
        });
    } catch (error) {
        console.error('❌ خطأ في تنفيذ تغيير حالة القالب:', error);
        showError('حدث خطأ أثناء تحديث حالة القالب');
    }
}

// تحديث حالة القالب في الواجهة
function updateTemplateStatusInUI(templateId, newStatus) {
    try {
        const row = document.querySelector(`tr[data-template-id="${templateId}"]`);
        if (!row) {
            console.warn(`⚠️ لم يتم العثور على صف القالب: ${templateId}`);
            return;
        }

        // تحديث شارة الحالة
        const statusCell = row.cells[3];
        const statusBadge = statusCell.querySelector('.badge');
        if (statusBadge) {
            statusBadge.className = `badge bg-${newStatus ? 'success' : 'danger'}`;
            statusBadge.textContent = newStatus ? 'نشط' : 'غير نشط';
        }

        // تحديث زر التبديل
        const toggleBtn = row.querySelector('.toggle-btn');
        if (toggleBtn) {
            toggleBtn.dataset.status = newStatus ? '1' : '0';
            toggleBtn.title = newStatus ? 'إلغاء التفعيل' : 'تفعيل';

            const icon = toggleBtn.querySelector('i');
            if (icon) {
                icon.className = `fas ${newStatus ? 'fa-toggle-on' : 'fa-toggle-off'}`;
            }
        }

        console.log('✅ تم تحديث واجهة القالب بنجاح:', templateId, newStatus);
    } catch (error) {
        console.error('❌ خطأ في تحديث واجهة القالب:', error);
    }
}

// وظائف مساعدة لإدارة الواجهة
function showError(message) {
    try {
        console.error('❌ عرض رسالة خطأ:', message);
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'خطأ',
                text: message,
                icon: 'error',
                confirmButtonText: 'موافق',
                confirmButtonColor: '#dc3545'
            });
        } else {
            console.warn('⚠️ SweetAlert غير متوفر، استخدام alert عادي');
            alert(`خطأ: ${message}`);
        }
    } catch (error) {
        console.error('❌ خطأ في عرض رسالة الخطأ:', error);
        alert(`خطأ: ${message}`);
    }
}

function showSuccess(message) {
    try {
        console.log('✅ عرض رسالة نجاح:', message);
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'نجح',
                text: message,
                icon: 'success',
                timer: 2000,
                showConfirmButton: false
            });
        } else {
            console.warn('⚠️ SweetAlert غير متوفر، استخدام alert عادي');
            alert(`نجح: ${message}`);
        }
    } catch (error) {
        console.error('❌ خطأ في عرض رسالة النجاح:', error);
        alert(`نجح: ${message}`);
    }
}

function showEmptyState() {
    try {
        console.log('📭 عرض حالة عدم وجود قوالب');
        const emptyState = document.getElementById('empty-state');
        const tableCard = document.getElementById('templates-table-card');

        if (emptyState) {
            emptyState.style.display = 'block';
            console.log('✅ تم عرض حالة عدم وجود قوالب');
        } else {
            console.warn('⚠️ لم يتم العثور على عنصر empty-state');
        }

        if (tableCard) {
            tableCard.style.display = 'none';
            console.log('✅ تم إخفاء جدول القوالب');
        } else {
            console.warn('⚠️ لم يتم العثور على عنصر templates-table-card');
        }
    } catch (error) {
        console.error('❌ خطأ في عرض حالة عدم وجود قوالب:', error);
    }
}

function updateTableNumbers() {
    try {
        console.log('🔢 تحديث أرقام الجدول');
        const rows = document.querySelectorAll('#templates-table-body tr');

        if (!rows.length) {
            console.warn('⚠️ لا توجد صفوف لتحديث أرقامها');
            return;
        }

        rows.forEach((row, index) => {
            try {
                const numberBadge = row.querySelector('.badge.bg-light');
                if (numberBadge) {
                    numberBadge.textContent = index + 1;
                } else {
                    console.warn(`⚠️ لم يتم العثور على شارة الرقم في الصف ${index}`);
                }
            } catch (rowError) {
                console.error(`❌ خطأ في تحديث رقم الصف ${index}:`, rowError);
            }
        });

        console.log(`✅ تم تحديث أرقام ${rows.length} صف`);
    } catch (error) {
        console.error('❌ خطأ في تحديث أرقام الجدول:', error);
    }
}

function updateTableStats() {
    try {
        console.log('📊 تحديث إحصائيات الجدول');
        const remainingCount = document.querySelectorAll('#templates-table-body tr').length;
        const showingCount = document.getElementById('showing-count');
        const totalCount = document.getElementById('total-count');

        if (showingCount) {
            showingCount.textContent = remainingCount;
            console.log(`✅ تم تحديث عدد القوالب المعروضة: ${remainingCount}`);
        } else {
            console.warn('⚠️ لم يتم العثور على عنصر showing-count');
        }

        if (totalCount) {
            totalCount.textContent = window.templatesData.totalCount || remainingCount;
            console.log(`✅ تم تحديث العدد الإجمالي: ${window.templatesData.totalCount || remainingCount}`);
        } else {
            console.warn('⚠️ لم يتم العثور على عنصر total-count');
        }
    } catch (error) {
        console.error('❌ خطأ في تحديث إحصائيات الجدول:', error);
    }
}

// تحديث حالة الرؤية (عرض الجدول أو الحالة الفارغة)
function updateVisibilityState(visibleCount) {
    try {
        console.log('👁️ تحديث حالة الرؤية، عدد القوالب المرئية:', visibleCount);

        const tableCard = document.getElementById('templates-table-card');
        const emptyState = document.getElementById('empty-state');
        const noResultsState = document.getElementById('no-results-state');

        if (visibleCount === 0) {
            // إخفاء الجدول وعرض حالة عدم وجود نتائج
            if (tableCard) {
                tableCard.style.display = 'none';
                console.log('✅ تم إخفاء جدول القوالب');
            }

            if (noResultsState) {
                noResultsState.style.display = 'block';
                console.log('✅ تم عرض حالة عدم وجود نتائج');
            } else if (emptyState) {
                emptyState.style.display = 'block';
                console.log('✅ تم عرض الحالة الفارغة');
            }
        } else {
            // عرض الجدول وإخفاء الحالات الفارغة
            if (tableCard) {
                tableCard.style.display = 'block';
                console.log('✅ تم عرض جدول القوالب');
            }

            if (emptyState) {
                emptyState.style.display = 'none';
            }

            if (noResultsState) {
                noResultsState.style.display = 'none';
            }
        }

        // تحديث الإحصائيات
        updateTableStats();

        console.log(`✅ تم تحديث حالة الرؤية بنجاح، القوالب المرئية: ${visibleCount}`);
    } catch (error) {
        console.error('❌ خطأ في تحديث حالة الرؤية:', error);
    }
}

// وظيفة ترتيب الجدول
function sortTable(column) {
    try {
        console.log(`🔄 ترتيب جدول قوالب التسعير حسب: ${column}`);

        const tbody = document.getElementById('templates-table-body');
        if (!tbody) {
            console.error('❌ لم يتم العثور على جسم الجدول');
            return;
        }

        const rows = Array.from(tbody.querySelectorAll('tr'));
        if (rows.length === 0) {
            console.warn('⚠️ لا توجد صفوف للترتيب');
            return;
        }

        console.log(`📊 ترتيب ${rows.length} صف حسب العمود: ${column}`);

        let sortFunction;
        switch(column) {
            case 'name':
                sortFunction = (a, b) => {
                    try {
                        const nameA = a.cells[1]?.textContent?.trim() || '';
                        const nameB = b.cells[1]?.textContent?.trim() || '';
                        return nameA.localeCompare(nameB, 'ar');
                    } catch (error) {
                        console.error('❌ خطأ في ترتيب الأسماء:', error);
                        return 0;
                    }
                };
                break;
            case 'status':
                sortFunction = (a, b) => {
                    try {
                        const statusA = a.cells[3]?.textContent?.trim() || '';
                        const statusB = b.cells[3]?.textContent?.trim() || '';
                        return statusA.localeCompare(statusB, 'ar');
                    } catch (error) {
                        console.error('❌ خطأ في ترتيب الحالات:', error);
                        return 0;
                    }
                };
                break;
            case 'created_at':
                sortFunction = (a, b) => {
                    try {
                        const dateA = new Date(a.cells[4]?.textContent?.trim() || 0);
                        const dateB = new Date(b.cells[4]?.textContent?.trim() || 0);
                        return dateB - dateA; // ترتيب تنازلي (الأحدث أولاً)
                    } catch (error) {
                        console.error('❌ خطأ في ترتيب التواريخ:', error);
                        return 0;
                    }
                };
                break;
            case 'updated_at':
                sortFunction = (a, b) => {
                    try {
                        const dateA = new Date(a.cells[5]?.textContent?.trim() || 0);
                        const dateB = new Date(b.cells[5]?.textContent?.trim() || 0);
                        return dateB - dateA; // ترتيب تنازلي (الأحدث أولاً)
                    } catch (error) {
                        console.error('❌ خطأ في ترتيب تواريخ التحديث:', error);
                        return 0;
                    }
                };
                break;
            default:
                console.warn(`⚠️ عمود ترتيب غير مدعوم: ${column}`);
                return;
        }

        // تطبيق الترتيب مع تأثير بصري
        tbody.style.opacity = '0.7';
        tbody.style.transition = 'opacity 0.3s ease';

        setTimeout(() => {
            try {
                rows.sort(sortFunction);
                rows.forEach(row => tbody.appendChild(row));
                updateTableNumbers(); // تحديث أرقام الصفوف بعد الترتيب
                tbody.style.opacity = '1';
                console.log(`✅ تم ترتيب ${rows.length} قالب حسب ${column}`);
            } catch (sortError) {
                console.error('❌ خطأ في تطبيق الترتيب:', sortError);
                tbody.style.opacity = '1';
            }
        }, 150);
    } catch (error) {
        console.error('❌ خطأ في ترتيب الجدول:', error);
    }
}

// وظيفة إعادة تعيين الفلاتر
function resetFilters() {
    try {
        console.log('🔄 إعادة تعيين جميع الفلاتر');

        // إعادة تعيين حقل البحث
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.value = '';
            console.log('✅ تم إعادة تعيين حقل البحث');
        }

        // إعادة تعيين فلتر الفئة
        const categoryFilter = document.getElementById('category-filter');
        if (categoryFilter) {
            categoryFilter.value = '';
            console.log('✅ تم إعادة تعيين فلتر الفئة');
        }

        // إعادة تعيين فلتر الحالة
        const statusFilter = document.getElementById('status-filter');
        if (statusFilter) {
            statusFilter.value = '';
            console.log('✅ تم إعادة تعيين فلتر الحالة');
        }

        // إعادة تعيين التبويب النشط إلى "جميع القوالب"
        document.querySelectorAll('#templateTabs .nav-link').forEach(tab => {
            tab.classList.remove('active');
        });

        const allTab = document.querySelector('#templateTabs .nav-link[data-filter="all"]');
        if (allTab) {
            allTab.classList.add('active');
            console.log('✅ تم إعادة تعيين التبويب النشط إلى "جميع القوالب"');
        }

        // عرض جميع الصفوف
        const rows = document.querySelectorAll('#templates-table-body tr');
        rows.forEach(row => {
            row.style.display = '';
        });

        // تحديث حالة الرؤية
        updateVisibilityState(rows.length);

        console.log('✅ تم إعادة تعيين جميع الفلاتر بنجاح');
        showSuccess('تم إعادة تعيين الفلاتر بنجاح');
    } catch (error) {
        console.error('❌ خطأ في إعادة تعيين الفلاتر:', error);
        showError('حدث خطأ أثناء إعادة تعيين الفلاتر');
    }
}

// وظائف الإجراءات المجمعة
function bulkActivate() {
    try {
        console.log('🔄 تفعيل القوالب المحددة');
        const selectedTemplates = getSelectedTemplates();

        if (selectedTemplates.length === 0) {
            console.warn('⚠️ لم يتم تحديد أي قوالب للتفعيل');
            showError('يرجى تحديد قالب واحد على الأقل للتفعيل');
            return;
        }

        console.log(`📋 تم تحديد ${selectedTemplates.length} قالب للتفعيل:`, selectedTemplates);

        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'تفعيل القوالب المحددة؟',
                text: `سيتم تفعيل ${selectedTemplates.length} قالب`,
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'نعم، فعل القوالب',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#28a745'
            }).then((result) => {
                if (result.isConfirmed) {
                    performBulkActivation(selectedTemplates);
                }
            });
        } else {
            console.warn('⚠️ SweetAlert غير متوفر، استخدام confirm عادي');
            if (confirm(`هل تريد تفعيل ${selectedTemplates.length} قالب؟`)) {
                performBulkActivation(selectedTemplates);
            }
        }
    } catch (error) {
        console.error('❌ خطأ في تفعيل القوالب المحددة:', error);
        showError('حدث خطأ أثناء تفعيل القوالب');
    }
}

// تنفيذ عملية التفعيل المجمع
function performBulkActivation(selectedTemplates) {
    try {
        console.log('🔄 تنفيذ تفعيل مجمع للقوالب:', selectedTemplates);

        // عرض مؤشر التحميل
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'جاري تفعيل القوالب...',
                text: `تفعيل ${selectedTemplates.length} قالب`,
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
        }

        // محاكاة عملية التفعيل (يمكن استبدالها بطلب AJAX حقيقي)
        setTimeout(() => {
            selectedTemplates.forEach(templateId => {
                updateTemplateStatusInUI(templateId, true);
            });

            showSuccess(`تم تفعيل ${selectedTemplates.length} قالب بنجاح`);
            console.log('✅ تم تفعيل القوالب المحددة بنجاح');
        }, 1500);
    } catch (error) {
        console.error('❌ خطأ في تنفيذ التفعيل المجمع:', error);
        showError('حدث خطأ أثناء تفعيل القوالب');
    }
}

// الحصول على القوالب المحددة
function getSelectedTemplates() {
    try {
        const checkboxes = document.querySelectorAll('.template-checkbox:checked');
        const selectedIds = Array.from(checkboxes).map(cb => {
            const row = cb.closest('tr');
            return row ? row.dataset.templateId : null;
        }).filter(id => id !== null);

        console.log(`📋 تم العثور على ${selectedIds.length} قالب محدد:`, selectedIds);
        return selectedIds;
    } catch (error) {
        console.error('❌ خطأ في الحصول على القوالب المحددة:', error);
        return [];
    }
}

// وظيفة إلغاء التفعيل المجمع
function bulkDeactivate() {
    try {
        console.log('🔄 إلغاء تفعيل القوالب المحددة');
        const selectedTemplates = getSelectedTemplates();

        if (selectedTemplates.length === 0) {
            console.warn('⚠️ لم يتم تحديد أي قوالب لإلغاء التفعيل');
            showError('يرجى تحديد قالب واحد على الأقل لإلغاء التفعيل');
            return;
        }

        console.log(`📋 تم تحديد ${selectedTemplates.length} قالب لإلغاء التفعيل:`, selectedTemplates);

        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'إلغاء تفعيل القوالب المحددة؟',
                text: `سيتم إلغاء تفعيل ${selectedTemplates.length} قالب`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'نعم، ألغ التفعيل',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#ffc107'
            }).then((result) => {
                if (result.isConfirmed) {
                    performBulkDeactivation(selectedTemplates);
                }
            });
        } else {
            console.warn('⚠️ SweetAlert غير متوفر، استخدام confirm عادي');
            if (confirm(`هل تريد إلغاء تفعيل ${selectedTemplates.length} قالب؟`)) {
                performBulkDeactivation(selectedTemplates);
            }
        }
    } catch (error) {
        console.error('❌ خطأ في إلغاء تفعيل القوالب المحددة:', error);
        showError('حدث خطأ أثناء إلغاء تفعيل القوالب');
    }
}

// تنفيذ عملية إلغاء التفعيل المجمع
function performBulkDeactivation(selectedTemplates) {
    try {
        console.log('🔄 تنفيذ إلغاء تفعيل مجمع للقوالب:', selectedTemplates);

        // عرض مؤشر التحميل
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'جاري إلغاء تفعيل القوالب...',
                text: `إلغاء تفعيل ${selectedTemplates.length} قالب`,
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
        }

        // محاكاة عملية إلغاء التفعيل (يمكن استبدالها بطلب AJAX حقيقي)
        setTimeout(() => {
            selectedTemplates.forEach(templateId => {
                updateTemplateStatusInUI(templateId, false);
            });

            showSuccess(`تم إلغاء تفعيل ${selectedTemplates.length} قالب بنجاح`);
            console.log('✅ تم إلغاء تفعيل القوالب المحددة بنجاح');
        }, 1500);
    } catch (error) {
        console.error('❌ خطأ في تنفيذ إلغاء التفعيل المجمع:', error);
        showError('حدث خطأ أثناء إلغاء تفعيل القوالب');
    }
}

// وظيفة الحذف المجمع
function bulkDelete() {
    try {
        console.log('🗑️ حذف القوالب المحددة');
        const selectedTemplates = getSelectedTemplates();

        if (selectedTemplates.length === 0) {
            console.warn('⚠️ لم يتم تحديد أي قوالب للحذف');
            showError('يرجى تحديد قالب واحد على الأقل للحذف');
            return;
        }

        console.log(`📋 تم تحديد ${selectedTemplates.length} قالب للحذف:`, selectedTemplates);

        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'حذف القوالب المحددة؟',
                text: `سيتم حذف ${selectedTemplates.length} قالب نهائياً. لا يمكن التراجع عن هذا الإجراء.`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'نعم، احذف القوالب',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#dc3545',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    performBulkDeletion(selectedTemplates);
                }
            });
        } else {
            console.warn('⚠️ SweetAlert غير متوفر، استخدام confirm عادي');
            if (confirm(`هل تريد حذف ${selectedTemplates.length} قالب نهائياً؟`)) {
                performBulkDeletion(selectedTemplates);
            }
        }
    } catch (error) {
        console.error('❌ خطأ في حذف القوالب المحددة:', error);
        showError('حدث خطأ أثناء حذف القوالب');
    }
}

// تنفيذ عملية الحذف المجمع
function performBulkDeletion(selectedTemplates) {
    try {
        console.log('🗑️ تنفيذ حذف مجمع للقوالب:', selectedTemplates);

        // عرض مؤشر التحميل
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'جاري حذف القوالب...',
                text: `حذف ${selectedTemplates.length} قالب`,
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
        }

        // محاكاة عملية الحذف (يمكن استبدالها بطلب AJAX حقيقي)
        setTimeout(() => {
            selectedTemplates.forEach(templateId => {
                const row = document.querySelector(`tr[data-template-id="${templateId}"]`);
                if (row) {
                    row.style.transition = 'opacity 0.3s ease';
                    row.style.opacity = '0';
                    setTimeout(() => {
                        row.remove();
                    }, 300);
                }
            });

            // تحديث الإحصائيات
            setTimeout(() => {
                updateTableNumbers();
                updateTableStats();

                const remainingRows = document.querySelectorAll('#templates-table-body tr');
                if (remainingRows.length === 0) {
                    showEmptyState();
                }
            }, 400);

            showSuccess(`تم حذف ${selectedTemplates.length} قالب بنجاح`);
            console.log('✅ تم حذف القوالب المحددة بنجاح');
        }, 1500);
    } catch (error) {
        console.error('❌ خطأ في تنفيذ الحذف المجمع:', error);
        showError('حدث خطأ أثناء حذف القوالب');
    }
}

// وظيفة تبديل الإجراءات المجمعة
function toggleBulkActions() {
    try {
        const selectedCount = document.querySelectorAll('.template-checkbox:checked').length;
        const bulkActionsContainer = document.getElementById('bulk-actions-container');

        if (bulkActionsContainer) {
            if (selectedCount > 0) {
                bulkActionsContainer.style.display = 'block';
                console.log(`✅ تم عرض الإجراءات المجمعة، القوالب المحددة: ${selectedCount}`);
            } else {
                bulkActionsContainer.style.display = 'none';
                console.log('✅ تم إخفاء الإجراءات المجمعة');
            }
        }
    } catch (error) {
        console.error('❌ خطأ في تبديل الإجراءات المجمعة:', error);
    }
}

// وظيفة debounce للبحث
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function bulkDeactivate() {
    const selectedTemplates = getSelectedTemplates();
    if (selectedTemplates.length === 0) {
        showError(window.templatesMessages.info.selectTemplates);
        return;
    }

    if (typeof Swal !== 'undefined') {
        Swal.fire({
            title: 'إلغاء تفعيل القوالب المحددة؟',
            text: window.templatesMessages.confirm.bulkDeactivate,
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'نعم، ألغ التفعيل',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                console.log('إلغاء تفعيل القوالب:', selectedTemplates);
                showSuccess('تم إلغاء تفعيل القوالب المحددة بنجاح');
            }
        });
    }
}

function bulkDelete() {
    const selectedTemplates = getSelectedTemplates();
    if (selectedTemplates.length === 0) {
        showError(window.templatesMessages.info.selectTemplates);
        return;
    }

    if (typeof Swal !== 'undefined') {
        Swal.fire({
            title: 'حذف القوالب المحددة؟',
            text: window.templatesMessages.confirm.bulkDelete,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'نعم، احذف القوالب',
            cancelButtonText: 'إلغاء',
            confirmButtonColor: '#dc3545'
        }).then((result) => {
            if (result.isConfirmed) {
                selectedTemplates.forEach(templateId => {
                    const row = document.querySelector(`tr[data-template-id="${templateId}"]`);
                    if (row) row.remove();
                });
                updateTableNumbers();
                updateTableStats();
                showSuccess('تم حذف القوالب المحددة بنجاح');
            }
        });
    }
}

function getSelectedTemplates() {
    const checkboxes = document.querySelectorAll('.template-checkbox:checked');
    return Array.from(checkboxes).map(cb => cb.closest('tr').dataset.templateId);
}

function toggleBulkActions() {
    const selectedCount = document.querySelectorAll('.template-checkbox:checked').length;
    const bulkActions = document.getElementById('bulkActions');

    if (bulkActions) {
        bulkActions.style.display = selectedCount > 0 ? 'block' : 'none';
    }
}

// تهيئة الصفحة عند التحميل
document.addEventListener('DOMContentLoaded', function() {
    try {
        console.log('🚀 بدء تهيئة صفحة قوالب التسعير الموحد');
        console.log('📊 بيانات القوالب المحملة:', window.templatesData);
        console.log('🔗 روابط النظام:', window.templatesRoutes);
        console.log('👤 بيانات المستخدم:', window.currentUser);

        // التحقق من وجود البيانات الأساسية
        if (!window.templatesData) {
            console.error('❌ بيانات القوالب غير متوفرة');
            showError('فشل في تحميل بيانات القوالب');
            return;
        }

        if (!window.templatesRoutes) {
            console.error('❌ روابط النظام غير متوفرة');
            showError('فشل في تحميل روابط النظام');
            return;
        }

        // تهيئة أزرار الإنشاء
        console.log('🔧 تهيئة أزرار الإنشاء');
        const createBtnHeader = document.getElementById('create-template-btn-header');
        const createBtnEmpty = document.getElementById('create-template-btn-empty');

        if (createBtnHeader) {
            createBtnHeader.addEventListener('click', handleCreateTemplate);
            console.log('✅ تم ربط زر الإنشاء في الرأس');
        } else {
            console.warn('⚠️ لم يتم العثور على زر الإنشاء في الرأس');
        }

        if (createBtnEmpty) {
            createBtnEmpty.addEventListener('click', handleCreateTemplate);
            console.log('✅ تم ربط زر الإنشاء في الحالة الفارغة');
        } else {
            console.warn('⚠️ لم يتم العثور على زر الإنشاء في الحالة الفارغة');
        }

        // تهيئة أزرار الإجراءات في الجدول
        console.log('🔧 تهيئة أزرار الإجراءات في الجدول');
        document.addEventListener('click', function(e) {
            try {
                if (e.target.closest('.preview-btn')) {
                    const templateId = e.target.closest('.preview-btn').dataset.id;
                    console.log('👁️ تم النقر على زر المعاينة للقالب:', templateId);
                    showTemplatePreview(templateId);
                }

                if (e.target.closest('.edit-btn')) {
                    const templateId = e.target.closest('.edit-btn').dataset.id;
                    console.log('✏️ تم النقر على زر التعديل للقالب:', templateId);
                    editTemplate(templateId);
                }

                if (e.target.closest('.delete-btn')) {
                    const templateId = e.target.closest('.delete-btn').dataset.id;
                    console.log('🗑️ تم النقر على زر الحذف للقالب:', templateId);
                    deleteTemplate(templateId);
                }

                if (e.target.closest('.toggle-btn')) {
                    const templateId = e.target.closest('.toggle-btn').dataset.id;
                    const currentStatus = e.target.closest('.toggle-btn').dataset.status;
                    console.log('🔄 تم النقر على زر التبديل للقالب:', templateId, 'الحالة الحالية:', currentStatus);
                    toggleTemplateStatus(templateId, currentStatus);
                }
            } catch (error) {
                console.error('❌ خطأ في معالجة النقر على أزرار الإجراءات:', error);
                showError('حدث خطأ أثناء تنفيذ الإجراء');
            }
        });
        console.log('✅ تم تهيئة أزرار الإجراءات في الجدول');

        // تهيئة تحديد الكل
        console.log('🔧 تهيئة خانة تحديد الكل');
        const selectAllCheckbox = document.getElementById('select-all-checkbox');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                try {
                    console.log('☑️ تم تغيير حالة تحديد الكل:', this.checked);
                    const checkboxes = document.querySelectorAll('.template-checkbox');
                    checkboxes.forEach(cb => cb.checked = this.checked);
                    toggleBulkActions();
                    console.log(`✅ تم ${this.checked ? 'تحديد' : 'إلغاء تحديد'} ${checkboxes.length} قالب`);
                } catch (error) {
                    console.error('❌ خطأ في تحديد الكل:', error);
                }
            });
            console.log('✅ تم تهيئة خانة تحديد الكل');
        } else {
            console.warn('⚠️ لم يتم العثور على خانة تحديد الكل');
        }

        // تهيئة checkboxes الفردية
        console.log('🔧 تهيئة خانات التحديد الفردية');
        const individualCheckboxes = document.querySelectorAll('.template-checkbox');
        if (individualCheckboxes.length > 0) {
            individualCheckboxes.forEach((checkbox, index) => {
                checkbox.addEventListener('change', function() {
                    try {
                        console.log(`☑️ تم تغيير حالة تحديد القالب ${index}:`, this.checked);
                        toggleBulkActions();
                    } catch (error) {
                        console.error(`❌ خطأ في تحديد القالب ${index}:`, error);
                    }
                });
            });
            console.log(`✅ تم تهيئة ${individualCheckboxes.length} خانة تحديد فردية`);
        } else {
            console.warn('⚠️ لم يتم العثور على خانات تحديد فردية');
        }

        // تهيئة أزرار الإجراءات المجمعة
        console.log('🔧 تهيئة أزرار الإجراءات المجمعة');
        const bulkActivateBtn = document.getElementById('bulk-activate-btn');
        const bulkDeactivateBtn = document.getElementById('bulk-deactivate-btn');
        const bulkDeleteBtn = document.getElementById('bulk-delete-btn');

        if (bulkActivateBtn) {
            bulkActivateBtn.addEventListener('click', bulkActivate);
            console.log('✅ تم ربط زر التفعيل المجمع');
        } else {
            console.warn('⚠️ لم يتم العثور على زر التفعيل المجمع');
        }

        if (bulkDeactivateBtn) {
            bulkDeactivateBtn.addEventListener('click', bulkDeactivate);
            console.log('✅ تم ربط زر إلغاء التفعيل المجمع');
        } else {
            console.warn('⚠️ لم يتم العثور على زر إلغاء التفعيل المجمع');
        }

        if (bulkDeleteBtn) {
            bulkDeleteBtn.addEventListener('click', bulkDelete);
            console.log('✅ تم ربط زر الحذف المجمع');
        } else {
            console.warn('⚠️ لم يتم العثور على زر الحذف المجمع');
        }

        // تهيئة ترتيب الجدول
        console.log('🔧 تهيئة ترتيب الجدول');
        const sortableHeaders = document.querySelectorAll('th[data-sort]');
        if (sortableHeaders.length > 0) {
            sortableHeaders.forEach((header, index) => {
                header.addEventListener('click', function() {
                    try {
                        const column = this.dataset.sort;
                        console.log(`📊 تم النقر على رأس العمود للترتيب:`, column);
                        sortTable(column);
                    } catch (error) {
                        console.error(`❌ خطأ في ترتيب العمود ${index}:`, error);
                    }
                });
                header.style.cursor = 'pointer';
            });
            console.log(`✅ تم تهيئة ${sortableHeaders.length} رأس عمود قابل للترتيب`);
        } else {
            console.warn('⚠️ لم يتم العثور على رؤوس أعمدة قابلة للترتيب');
        }

        // تهيئة التبويبات
        console.log('🔧 تهيئة التبويبات');
        const tabLinks = document.querySelectorAll('#templateTabs .nav-link');
        if (tabLinks.length > 0) {
            tabLinks.forEach((tab, index) => {
                tab.addEventListener('click', function(e) {
                    try {
                        e.preventDefault();
                        const filter = this.dataset.filter;
                        console.log(`📑 تم النقر على التبويب ${index}:`, filter);

                        // إزالة الفئة النشطة من جميع التبويبات
                        document.querySelectorAll('#templateTabs .nav-link').forEach(t => t.classList.remove('active'));

                        // إضافة الفئة النشطة للتبويب المحدد
                        this.classList.add('active');

                        // تطبيق الفلتر
                        filterTemplatesByTab(filter);
                        console.log(`✅ تم تطبيق فلتر التبويب: ${filter}`);
                    } catch (error) {
                        console.error(`❌ خطأ في معالجة النقر على التبويب ${index}:`, error);
                        showError('حدث خطأ أثناء تغيير التبويب');
                    }
                });
            });
            console.log(`✅ تم تهيئة ${tabLinks.length} تبويب`);
        } else {
            console.warn('⚠️ لم يتم العثور على تبويبات');
        }

        // تهيئة البحث والفلاتر
        console.log('🔧 تهيئة البحث والفلاتر');
        const searchInput = document.getElementById('search-input');
        const categoryFilter = document.getElementById('category-filter');
        const statusFilter = document.getElementById('status-filter');
        const sortFilter = document.getElementById('sort-filter');
        const resetBtn = document.getElementById('reset-filters-btn');

        if (searchInput) {
            searchInput.addEventListener('input', debounce(function() {
                try {
                    console.log('🔍 تم تغيير نص البحث:', searchInput.value);
                    applyFilters();
                } catch (error) {
                    console.error('❌ خطأ في البحث:', error);
                }
            }, 300));
            console.log('✅ تم ربط حقل البحث');
        } else {
            console.warn('⚠️ لم يتم العثور على حقل البحث');
        }

        if (categoryFilter) {
            categoryFilter.addEventListener('change', function() {
                try {
                    console.log('📂 تم تغيير فلتر الفئة:', this.value);
                    applyFilters();
                } catch (error) {
                    console.error('❌ خطأ في فلتر الفئة:', error);
                }
            });
            console.log('✅ تم ربط فلتر الفئة');
        } else {
            console.warn('⚠️ لم يتم العثور على فلتر الفئة');
        }

        if (statusFilter) {
            statusFilter.addEventListener('change', function() {
                try {
                    console.log('📊 تم تغيير فلتر الحالة:', this.value);
                    applyFilters();
                } catch (error) {
                    console.error('❌ خطأ في فلتر الحالة:', error);
                }
            });
            console.log('✅ تم ربط فلتر الحالة');
        } else {
            console.warn('⚠️ لم يتم العثور على فلتر الحالة');
        }

        if (sortFilter) {
            sortFilter.addEventListener('change', function() {
                try {
                    console.log('🔄 تم تغيير ترتيب الجدول:', this.value);
                    sortTable(this.value);
                } catch (error) {
                    console.error('❌ خطأ في ترتيب الجدول:', error);
                }
            });
            console.log('✅ تم ربط فلتر الترتيب');
        } else {
            console.warn('⚠️ لم يتم العثور على فلتر الترتيب');
        }

        if (resetBtn) {
            resetBtn.addEventListener('click', function() {
                try {
                    console.log('🔄 تم النقر على زر إعادة تعيين الفلاتر');
                    resetFilters();
                } catch (error) {
                    console.error('❌ خطأ في إعادة تعيين الفلاتر:', error);
                }
            });
            console.log('✅ تم ربط زر إعادة تعيين الفلاتر');
        } else {
            console.warn('⚠️ لم يتم العثور على زر إعادة تعيين الفلاتر');
        }

        // عرض الجدول أو الحالة الفارغة
        console.log('🔧 تحديد حالة العرض الأولية');
        if (window.templatesData.totalCount === 0) {
            console.log('📭 لا توجد قوالب، عرض الحالة الفارغة');
            showEmptyState();
        } else {
            console.log(`📊 يوجد ${window.templatesData.totalCount} قالب، عرض الجدول`);
            const tableCard = document.getElementById('templates-table-card');
            if (tableCard) {
                tableCard.style.display = 'block';
                console.log('✅ تم عرض جدول القوالب');
            } else {
                console.warn('⚠️ لم يتم العثور على عنصر جدول القوالب');
            }
        }

        // إضافة معلومات التشخيص النهائية
        console.log('📋 ملخص التهيئة:');
        console.log(`  - عدد القوالب المحملة: ${window.templatesData.totalCount}`);
        console.log(`  - عدد القوالب النشطة: ${window.templatesData.activeCount}`);
        console.log(`  - عدد التبويبات: ${document.querySelectorAll('#templateTabs .nav-link').length}`);
        console.log(`  - عدد صفوف الجدول: ${document.querySelectorAll('#templates-table-body tr').length}`);
        console.log(`  - عدد أزرار الإجراءات: ${document.querySelectorAll('.preview-btn, .edit-btn, .delete-btn, .toggle-btn').length}`);

        console.log('🎉 تم تهيئة جميع وظائف قوالب التسعير بنجاح');

    } catch (error) {
        console.error('❌ خطأ فادح في تهيئة صفحة قوالب التسعير:', error);
        showError('حدث خطأ في تحميل الصفحة. يرجى إعادة تحميل الصفحة.');

        // محاولة عرض معلومات التشخيص حتى في حالة الخطأ
        try {
            console.log('🔍 معلومات التشخيص في حالة الخطأ:');
            console.log('  - window.templatesData:', window.templatesData);
            console.log('  - window.templatesRoutes:', window.templatesRoutes);
            console.log('  - window.currentUser:', window.currentUser);
            console.log('  - SweetAlert متوفر:', typeof Swal !== 'undefined');
        } catch (diagError) {
            console.error('❌ خطأ في معلومات التشخيص:', diagError);
        }
    }
});

// وظائف الفلترة والبحث
function filterTemplatesByTab(filter) {
    try {
        console.log('🔍 فلترة القوالب حسب التبويب:', filter);

        const rows = document.querySelectorAll('#templates-table-body tr');
        let visibleCount = 0;

        if (!rows.length) {
            console.warn('⚠️ لم يتم العثور على صفوف في الجدول');
            return;
        }

        rows.forEach((row, index) => {
            try {
                let shouldShow = true;
                const templateId = row.dataset.templateId;

                if (filter !== 'all') {
                    // البحث عن القالب في البيانات المحملة
                    const template = window.templatesData.templates.find(t => t.id == templateId);

                    if (template) {
                        switch(filter) {
                            case 'popular':
                                shouldShow = template.category === 'popular';
                                break;
                            case 'recommended':
                                shouldShow = template.category === 'recommended';
                                break;
                            case 'custom':
                                shouldShow = template.category === 'custom' || !template.is_predefined;
                                break;
                            case 'recent':
                                // عرض القوالب المضافة في آخر 30 يوم
                                const createdDate = new Date(template.created_at);
                                const thirtyDaysAgo = new Date();
                                thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                                shouldShow = createdDate >= thirtyDaysAgo;
                                break;
                            default:
                                shouldShow = true;
                        }
                    } else {
                        console.warn(`⚠️ لم يتم العثور على بيانات القالب: ${templateId}`);
                        shouldShow = true; // عرض القالب في حالة عدم وجود بيانات
                    }
                }

                if (shouldShow) {
                    row.style.display = '';
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                }
            } catch (rowError) {
                console.error(`❌ خطأ في معالجة الصف ${index}:`, rowError);
                // عرض الصف في حالة الخطأ
                row.style.display = '';
                visibleCount++;
            }
        });

        updateVisibilityState(visibleCount);
        console.log(`✅ تم فلترة ${visibleCount} قالب من أصل ${rows.length} باستخدام فلتر: ${filter}`);

    } catch (error) {
        console.error('❌ خطأ في فلترة القوالب حسب التبويب:', error);
        // في حالة الخطأ، عرض جميع الصفوف
        const rows = document.querySelectorAll('#templates-table-body tr');
        rows.forEach(row => row.style.display = '');
        updateVisibilityState(rows.length);
    }
}

function applyFilters() {
    try {
        console.log('🔍 تطبيق الفلاتر');

        const searchInput = document.getElementById('search-input');
        const categoryFilterEl = document.getElementById('category-filter');
        const statusFilterEl = document.getElementById('status-filter');

        const searchTerm = searchInput?.value.toLowerCase().trim() || '';
        const categoryFilter = categoryFilterEl?.value || '';
        const statusFilter = statusFilterEl?.value || '';

        console.log('🔍 معايير الفلترة:', { searchTerm, categoryFilter, statusFilter });

        const rows = document.querySelectorAll('#templates-table-body tr');
        let visibleCount = 0;

        if (!rows.length) {
            console.warn('⚠️ لم يتم العثور على صفوف في الجدول للفلترة');
            return;
        }

        rows.forEach((row, index) => {
            try {
                const templateId = row.dataset.templateId;
                const name = row.cells[1]?.textContent?.toLowerCase().trim() || '';
                const description = row.cells[2]?.textContent?.toLowerCase().trim() || '';
                const statusCell = row.cells[3]?.textContent?.toLowerCase().trim() || '';

                let shouldShow = true;

                // فلترة البحث
                if (searchTerm) {
                    const matchesSearch = name.includes(searchTerm) ||
                                        description.includes(searchTerm) ||
                                        templateId?.toLowerCase().includes(searchTerm);
                    if (!matchesSearch) {
                        shouldShow = false;
                    }
                }

                // فلترة الفئة
                if (categoryFilter && shouldShow) {
                    const template = window.templatesData.templates.find(t => t.id == templateId);
                    if (template) {
                        if (template.category !== categoryFilter) {
                            shouldShow = false;
                        }
                    }
                }

                // فلترة الحالة
                if (statusFilter && shouldShow) {
                    const isActive = statusCell.includes('active') || statusCell.includes('نشط');
                    if ((statusFilter === 'active' && !isActive) ||
                        (statusFilter === 'inactive' && isActive)) {
                        shouldShow = false;
                    }
                }

                if (shouldShow) {
                    row.style.display = '';
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                }
            } catch (rowError) {
                console.error(`❌ خطأ في معالجة الصف ${index} أثناء الفلترة:`, rowError);
                // عرض الصف في حالة الخطأ
                row.style.display = '';
                visibleCount++;
            }
        });

        updateVisibilityState(visibleCount);
        console.log(`✅ تم فلترة ${visibleCount} قالب من أصل ${rows.length}`);

    } catch (error) {
        console.error('❌ خطأ في تطبيق الفلاتر:', error);
        // في حالة الخطأ، عرض جميع الصفوف
        const rows = document.querySelectorAll('#templates-table-body tr');
        rows.forEach(row => row.style.display = '');
        updateVisibilityState(rows.length);
    }
}

function resetFilters() {
    console.log('🔄 إعادة تعيين الفلاتر');

    // إعادة تعيين عناصر البحث والفلاتر
    const searchInput = document.getElementById('search-input');
    const categoryFilter = document.getElementById('category-filter');
    const statusFilter = document.getElementById('status-filter');
    const sortFilter = document.getElementById('sort-filter');

    if (searchInput) searchInput.value = '';
    if (categoryFilter) categoryFilter.value = '';
    if (statusFilter) statusFilter.value = '';
    if (sortFilter) sortFilter.value = 'name';

    // إعادة تعيين التبويبات
    document.querySelectorAll('#templateTabs .nav-link').forEach(link => {
        link.classList.remove('active');
        if (link.dataset.filter === 'all') {
            link.classList.add('active');
        }
    });

    // إظهار جميع الصفوف
    document.querySelectorAll('#templates-table-body tr').forEach(row => {
        row.style.display = '';
    });

    updateVisibilityState(document.querySelectorAll('#templates-table-body tr').length);
}

function updateVisibilityState(visibleCount) {
    const emptyState = document.getElementById('empty-state');
    const noResultsState = document.getElementById('no-results-state');
    const tableCard = document.getElementById('templates-table-card');

    if (visibleCount === 0) {
        if (tableCard) tableCard.style.display = 'none';
        if (noResultsState) noResultsState.style.display = 'block';
        if (emptyState) emptyState.style.display = 'none';
    } else {
        if (tableCard) tableCard.style.display = 'block';
        if (noResultsState) noResultsState.style.display = 'none';
        if (emptyState) emptyState.style.display = 'none';
    }
}

// وظيفة مساعدة للتأخير (debounce)
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// تسجيل تحميل الوظائف
console.log('✅ تم تحميل جميع وظائف قوالب التسعير الأساسية');
</script>
@endpush
