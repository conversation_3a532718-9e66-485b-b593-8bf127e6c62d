{{--
    صفحة قوالب التسعير الموحد (نسخة مُعادة الهيكلة)
    @version 4.0
--}}

@extends('layouts.app')

@push('styles')
    <link href="{{ asset('css/select2.min.css') }}" rel="stylesheet">
    <link href="{{ asset('css/sweetalert2.min.css') }}" rel="stylesheet">
    <link href="{{ asset('css/unified-pricing-enhanced.css') }}" rel="stylesheet">
    <link href="{{ asset('css/unified-pricing/templates-table.css') }}" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" crossorigin="anonymous">
@endpush

@section('content')
<div class="page-wrapper">
    
    {{-- شريط العنوان والتنقل --}}
    <div class="row page-titles">
        <div class="col-md-5 align-self-center">
            <h3 class="text-themecolor">قوالب التسعير الموحد</h3>
        </div>
        <div class="col-md-7 align-self-center">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{url('/dashboard')}}">لوحة التحكم</a></li>
                <li class="breadcrumb-item"><a href="{{url('/unified-pricing')}}">التسعير الموحد</a></li>
                <li class="breadcrumb-item active">قوالب التسعير</li>
            </ol>
        </div>
    </div>

    <div class="container-fluid">
        
        {{-- قسم العنوان الرئيسي وزر الإنشاء --}}
        <div class="templates-header-section">
            <div class="row align-items-center">
                <div class="col-md-8">
                     {{-- ... (محتوى العنوان كما هو) --}}
                </div>
                <div class="col-md-4 text-end">
                    <button type="button" class="btn btn-primary btn-lg" id="create-template-btn-header">
                        <i class="fas fa-plus me-2"></i>إنشاء قالب جديد
                    </button>
                </div>
            </div>
        </div>

        {{-- (الإحصائيات السريعة كما هي) --}}

        {{-- حالة التحميل --}}
        <div id="loading-state" class="templates-loading-state" style="display: none;">
            {{-- ... (محتوى التحميل كما هو) --}}
        </div>

        {{-- حالة عدم وجود قوالب --}}
        <div id="empty-state" class="templates-empty-state" style="display: none;">
            <div class="templates-empty-icon"><i class="fas fa-layer-group"></i></div>
            <h4 class="empty-state-title">لا توجد قوالب تسعير</h4>
            <p class="empty-state-description">لم يتم إنشاء أي قوالب تسعير بعد.</p>
            <button type="button" class="btn btn-primary" id="create-template-btn-empty">
                <i class="fas fa-plus me-2"></i>إنشاء أول قالب
            </button>
        </div>

        {{-- أدوات البحث والفلترة --}}
        <div class="templates-filters-card card mb-4">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label for="search-input" class="form-label">البحث</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" id="search-input" placeholder="البحث في القوالب...">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label for="category-filter" class="form-label">الفئة</label>
                        <select class="form-select" id="category-filter">
                            <option value="">جميع الفئات</option>
                            <option value="popular">شائع</option>
                            <option value="recommended">موصى به</option>
                            <option value="custom">مخصص</option>
                            <option value="default">افتراضي</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="status-filter" class="form-label">الحالة</label>
                        <select class="form-select" id="status-filter">
                            <option value="">جميع الحالات</option>
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="sort-filter" class="form-label">الترتيب</label>
                        <select class="form-select" id="sort-filter">
                            <option value="name">الاسم</option>
                            <option value="created_at">تاريخ الإنشاء</option>
                            <option value="updated_at">آخر تحديث</option>
                        </select>
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="button" class="btn btn-outline-secondary w-100" id="reset-filters-btn">
                            <i class="fas fa-refresh me-1"></i>إعادة تعيين
                        </button>
                    </div>
                </div>
            </div>
        </div>

        {{-- تبويبات الفلترة السريعة --}}
        <div class="templates-tabs-card card mb-4">
            <div class="card-body">
                <ul class="nav nav-pills mb-0" id="templateTabs">
                    <li class="nav-item">
                        <a class="nav-link active" href="#" data-filter="all">
                            <i class="fas fa-th-large me-2"></i>جميع القوالب
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-filter="popular">
                            <i class="fas fa-fire me-2"></i>الأكثر استخداماً
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-filter="recommended">
                            <i class="fas fa-star me-2"></i>موصى به
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-filter="custom">
                            <i class="fas fa-user-cog me-2"></i>مخصص
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-filter="recent">
                            <i class="fas fa-clock me-2"></i>الأحدث
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        {{-- Enhanced Templates Table --}}
        <div class="card templates-table-wrapper" id="templates-table-card">
            <div class="table-responsive">
                <table class="table table-hover mb-0" id="templates-table">
                    <thead>
                        <tr>
                            <th width="40px"><input type="checkbox" id="select-all-checkbox"></th>
                            <th data-sort="name">@lang('name') <i class="fas fa-sort"></i></th>
                            <th data-sort="description">@lang('description')</th>
                            <th data-sort="status">@lang('status')</th>
                            <th data-sort="created_at">@lang('created at')</th>
                            <th data-sort="updated_at">@lang('updated at')</th>
                            <th width="200px">@lang('actions')</th>
                        </tr>
                    </thead>
                    <tbody id="templates-table-body">
                        @foreach($templates as $template)
                        @php
                            $id = is_object($template) ? $template->id : ($template['id'] ?? null);
                            $name = is_object($template) ? $template->name : ($template['name'] ?? '');
                            $desc = is_object($template) ? $template->description : ($template['description'] ?? '');
                            $active = is_object($template) ? $template->is_active : ($template['is_active'] ?? false);
                        @endphp
                        <tr data-template-id="{{ $id }}">
                            <td><input type="checkbox" class="template-checkbox"></td>
                            <td>{{ $name }}</td>
                            <td>{{ $desc }}</td>
                            <td>
                                <span class="badge bg-{{ $active ? 'success' : 'danger' }}">
                                    {{ $active ? __('active') : __('inactive') }}
                                </span>
                            </td>
                            <td>
                                @php
                                    $createdAt = is_object($template) ? $template->created_at : (isset($template['created_at'])
                                        ? (is_string($template['created_at']) ? \Illuminate\Support\Carbon::parse($template['created_at'])
                                        : $template['created_at']) : null);
                                @endphp
                                {{ optional($createdAt)->format('Y/m/d H:i') }}
                            </td>
                            <td>
                                @php
                                    $updatedAt = is_object($template) ? $template->updated_at : (isset($template['updated_at'])
                                        ? (is_string($template['updated_at']) ? \Illuminate\Support\Carbon::parse($template['updated_at'])
                                        : $template['updated_at']) : null);
                                @endphp
                                {{ optional($updatedAt)->format('Y/m/d H:i') }}
                            </td>
                            <td class="text-end">
                                <button class="btn btn-sm btn-outline-secondary preview-btn ms-1" data-id="{{ $id }}" title="@lang('Preview')">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-primary edit-btn ms-1" data-id="{{ $id }}" title="@lang('Edit')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-warning toggle-btn ms-1"
                                    data-id="{{ $id }}"
                                    data-status="{{ $active ? 1 : 0 }}"
                                    title="{{ $active ? __('Deactivate') : __('Activate') }}">
                                    <i class="fas {{ $active ? 'fa-toggle-on' : 'fa-toggle-off' }}"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger delete-btn ms-1" data-id="{{ $id }}" title="@lang('Delete')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            {{-- حالة عدم وجود نتائج بعد الفلترة --}}
            <div id="no-results-state" class="templates-empty-state" style="display: none;">
                {{-- ... (محتوى الحالة كما هو) --}}
            </div>
            
            {{-- أدوات الجدول السفلية --}}
            <div class="templates-table-footer">
                <div id="bulkActions" style="display: none;">
                    <button type="button" class="btn btn-sm btn-outline-success" id="bulk-activate-btn">تفعيل</button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="bulk-deactivate-btn">إلغاء تفعيل</button>
                    <button type="button" class="btn btn-sm btn-outline-danger" id="bulk-delete-btn">حذف</button>
                </div>
            </div>
        </div>

    </div>
</div>
@endsection

@push('scripts')
<!-- مكتبات أساسية من CDN للتأكد من التحميل -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<!-- ملفات نظام قوالب التسعير الموحد (مع fallback) -->
<script>
// التحقق من تحميل المكتبات الأساسية
if (typeof jQuery === 'undefined') {
    console.error('jQuery غير محمل!');
}
if (typeof Swal === 'undefined') {
    console.error('SweetAlert2 غير محمل!');
}
</script>

<script>
/**
 * إعدادات صفحة قوالب التسعير الموحد
 * تمرير البيانات من Laravel إلى JavaScript بشكل آمن ومنظم
 */

// تمرير بيانات القوالب من الخادم
window.templatesData = {
    templates: @json($templates ?? []),
    totalCount: {{ count($templates ?? []) }},
    activeCount: {{ collect($templates ?? [])->where('is_active', true)->count() }},
    categories: {
        popular: {{ collect($templates ?? [])->where('category', 'popular')->count() }},
        recommended: {{ collect($templates ?? [])->where('category', 'recommended')->count() }},
        custom: {{ collect($templates ?? [])->where('category', 'custom')->count() }},
        default: {{ collect($templates ?? [])->where('category', 'default')->count() }}
    }
};

// تمرير روابط النظام المخصصة لقوالب التسعير
window.templatesRoutes = {
    create: '{{ route("unified-pricing.templates.create") }}',
    edit: '{{ url("/unified-pricing/templates") }}',
    show: '{{ url("/unified-pricing/templates") }}',
    delete: '{{ url("/unified-pricing/templates") }}',
    use: '{{ url("/unified-pricing/api/templates") }}',
    duplicate: '{{ url("/unified-pricing/templates") }}',
    export: '{{ url("/unified-pricing/templates") }}'
};

// تمرير معلومات المستخدم الحالي
window.currentUser = {
    id: {{ auth()->id() ?? 'null' }},
    name: '{{ auth()->user()->name ?? "مستخدم غير معروف" }}',
    role: '{{ auth()->user()->role ?? "user" }}',
    permissions: @json(auth()->user()->permissions ?? [])
};

// إعدادات النظام المخصصة لقوالب التسعير
window.templatesSystemSettings = {
    locale: '{{ app()->getLocale() }}',
    timezone: '{{ config("app.timezone") }}',
    currency: '{{ config("app.currency", "SAR") }}',
    dateFormat: 'YYYY-MM-DD',
    timeFormat: 'HH:mm:ss',
    pagination: {
        perPage: 10,
        maxPerPage: 50
    },
    validation: {
        maxNameLength: 100,
        maxDescriptionLength: 500,
        minRating: 0,
        maxRating: 5
    }
};

// رسائل النظام المخصصة لقوالب التسعير
window.templatesMessages = {
    // رسائل النجاح
    success: {
        created: 'تم إنشاء قالب التسعير بنجاح',
        updated: 'تم تحديث قالب التسعير بنجاح',
        deleted: 'تم حذف قالب التسعير بنجاح',
        used: 'تم تطبيق قالب التسعير بنجاح',
        activated: 'تم تفعيل القالب بنجاح',
        deactivated: 'تم إلغاء تفعيل القالب بنجاح'
    },

    // رسائل التأكيد
    confirm: {
        delete: 'هل أنت متأكد من حذف قالب التسعير؟ لا يمكن التراجع عن هذا الإجراء.',
        use: 'هل تريد تطبيق هذا القالب؟ سيتم استخدام إعداداته في النظام.',
        bulkDelete: 'هل أنت متأكد من حذف القوالب المحددة؟',
        bulkActivate: 'هل تريد تفعيل جميع القوالب المحددة؟',
        bulkDeactivate: 'هل تريد إلغاء تفعيل جميع القوالب المحددة؟'
    },

    // رسائل الأخطاء
    error: {
        general: 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.',
        notFound: 'لم يتم العثور على قالب التسعير المطلوب',
        network: 'خطأ في الاتصال بالخادم. تحقق من اتصالك بالإنترنت.',
        validation: 'يرجى التحقق من البيانات المدخلة وإصلاح الأخطاء.',
        permission: 'ليس لديك صلاحية لتنفيذ هذا الإجراء.',
        inUse: 'لا يمكن حذف هذا القالب لأنه قيد الاستخدام حالياً.'
    },

    // رسائل المعلومات
    info: {
        loading: 'جاري تحميل قوالب التسعير...',
        noTemplates: 'لا توجد قوالب تسعير متاحة',
        noResults: 'لم يتم العثور على نتائج تطابق البحث',
        selectTemplates: 'يرجى تحديد قالب واحد على الأقل'
    }
};

// تسجيل معلومات التهيئة في وحدة التحكم
console.log('🎯 تم تهيئة صفحة قوالب التسعير الموحد:');
console.log('📊 عدد القوالب:', window.templatesData.totalCount);
console.log('✅ القوالب النشطة:', window.templatesData.activeCount);
console.log('👤 المستخدم الحالي:', window.currentUser.name);
console.log('🌐 اللغة:', window.templatesSystemSettings.locale);
console.log('💰 العملة:', window.templatesSystemSettings.currency);

/**
 * وظائف مخصصة لإدارة قوالب التسعير
 * تركز حصرياً على وظائف القوالب دون تداخل مع أنظمة أخرى
 */

// وظيفة إنشاء قالب تسعير جديد
function handleCreateTemplate() {
    console.log('🆕 بدء إنشاء قالب تسعير جديد');

    if (typeof Swal !== 'undefined') {
        Swal.fire({
            title: 'إنشاء قالب تسعير جديد',
            text: 'سيتم توجيهك إلى صفحة إنشاء قالب تسعير جديد',
            icon: 'info',
            showCancelButton: true,
            confirmButtonText: 'متابعة الإنشاء',
            cancelButtonText: 'إلغاء',
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#6c757d'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = window.templatesRoutes.create;
            }
        });
    } else {
        if (confirm('هل تريد إنشاء قالب تسعير جديد؟')) {
            window.location.href = window.templatesRoutes.create;
        }
    }
}

// وظيفة معاينة قالب التسعير
function showTemplatePreview(templateId) {
    try {
        console.log('👁️ معاينة قالب التسعير:', templateId);

        if (!templateId) {
            console.error('❌ معرف القالب مطلوب للمعاينة');
            showError('معرف القالب غير صحيح');
            return;
        }

        const template = window.templatesData.templates.find(t => t.id == templateId);

        if (!template) {
            console.error(`❌ لم يتم العثور على القالب: ${templateId}`);
            showError('لم يتم العثور على القالب المطلوب');
            return;
        }

        console.log('✅ تم العثور على القالب للمعاينة:', template);

        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'معاينة قالب التسعير',
                html: generateTemplatePreviewHTML(template),
                showCancelButton: true,
                confirmButtonText: 'استخدام القالب',
                cancelButtonText: 'إغلاق',
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d',
                width: '700px',
                customClass: {
                    htmlContainer: 'text-start'
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    useTemplate(templateId);
                }
            });
        } else {
            console.warn('⚠️ SweetAlert غير متوفر، استخدام alert عادي');
            alert(`معاينة القالب: ${template.name}\n\nالوصف: ${template.description || 'غير محدد'}`);
        }
    } catch (error) {
        console.error('❌ خطأ في معاينة القالب:', error);
        showError('حدث خطأ أثناء معاينة القالب');
    }
}

// وظيفة إنشاء HTML لمعاينة القالب
function generateTemplatePreviewHTML(template) {
    const statusNames = {
        'active': 'نشط',
        'inactive': 'غير نشط',
        'draft': 'مسودة'
    };

    return `
        <div class="row g-3">
            <div class="col-md-6">
                <h6 class="text-primary mb-3">
                    <i class="fas fa-info-circle me-2"></i>المعلومات الأساسية
                </h6>
                <p><strong>اسم القالب:</strong> ${template.name || 'غير محدد'}</p>
                <p><strong>الوصف:</strong> ${template.description || 'لا يوجد وصف'}</p>
                <p><strong>الحالة:</strong> ${template.is_active ? 'نشط' : 'غير نشط'}</p>
            </div>
            <div class="col-md-6">
                <h6 class="text-primary mb-3">
                    <i class="fas fa-chart-bar me-2"></i>الإحصائيات
                </h6>
                <p><strong>تاريخ الإنشاء:</strong> ${template.created_at || 'غير محدد'}</p>
                <p><strong>آخر تحديث:</strong> ${template.updated_at || 'غير محدد'}</p>
            </div>
        </div>
    `;
}

// وظيفة استخدام قالب التسعير
function useTemplate(templateId) {
    try {
        console.log('✅ استخدام قالب التسعير:', templateId);

        if (!templateId) {
            console.error('❌ معرف القالب مطلوب للاستخدام');
            showError('معرف القالب غير صحيح');
            return;
        }

        const template = window.templatesData.templates.find(t => t.id == templateId);

        if (!template) {
            console.error(`❌ لم يتم العثور على القالب: ${templateId}`);
            showError('لم يتم العثور على القالب المطلوب');
            return;
        }

        console.log('✅ تم العثور على القالب للاستخدام:', template);

        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'استخدام قالب التسعير؟',
                text: `سيتم تطبيق إعدادات قالب "${template.name}" في النظام`,
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'نعم، استخدم القالب',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d'
            }).then((result) => {
                if (result.isConfirmed) {
                    // إرسال طلب AJAX لاستخدام القالب
                    useTemplateAjax(templateId, template);
                }
            });
        } else {
            console.warn('⚠️ SweetAlert غير متوفر، استخدام confirm عادي');
            if (confirm(`هل تريد استخدام قالب "${template.name}"؟`)) {
                useTemplateAjax(templateId, template);
            }
        }
    } catch (error) {
        console.error('❌ خطأ في استخدام القالب:', error);
        showError('حدث خطأ أثناء استخدام القالب');
    }
}

// وظيفة إرسال طلب AJAX لاستخدام القالب
function useTemplateAjax(templateId, template) {
    try {
        console.log('📡 إرسال طلب استخدام القالب:', templateId);

        // عرض مؤشر التحميل
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'جاري تطبيق القالب...',
                text: 'يرجى الانتظار',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
        }

        // إرسال الطلب إلى الخادم
        fetch(`${window.templatesRoutes.use}/${templateId}/use`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            },
            body: JSON.stringify({
                name: `${template.name} - نسخة`,
                display_name: `${template.name} - نسخة`,
                description: `تم إنشاؤه من القالب: ${template.name}`,
                scope: template.scope || 'global'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('✅ تم تطبيق القالب بنجاح:', data);
                showSuccess(data.message || 'تم تطبيق القالب بنجاح');

                // التوجه إلى الصفحة المحددة إذا كانت متوفرة
                if (data.redirect) {
                    setTimeout(() => {
                        window.location.href = data.redirect;
                    }, 1500);
                }
            } else {
                console.error('❌ فشل في تطبيق القالب:', data);
                showError(data.message || 'فشل في تطبيق القالب');
            }
        })
        .catch(error => {
            console.error('❌ خطأ في طلب استخدام القالب:', error);
            showError('حدث خطأ أثناء تطبيق القالب');
        });
    } catch (error) {
        console.error('❌ خطأ في إرسال طلب استخدام القالب:', error);
        showError('حدث خطأ أثناء تطبيق القالب');
    }
}

// وظيفة تعديل قالب التسعير
function editTemplate(templateId) {
    try {
        console.log('✏️ تعديل قالب التسعير:', templateId);

        if (!templateId) {
            console.error('❌ معرف القالب مطلوب للتعديل');
            showError('معرف القالب غير صحيح');
            return;
        }

        const template = window.templatesData.templates.find(t => t.id == templateId);

        if (!template) {
            console.error(`❌ لم يتم العثور على القالب: ${templateId}`);
            showError('لم يتم العثور على القالب المطلوب');
            return;
        }

        console.log('✅ توجيه إلى صفحة تعديل القالب:', template);
        window.location.href = `${window.templatesRoutes.edit}/${templateId}/edit`;
    } catch (error) {
        console.error('❌ خطأ في تعديل القالب:', error);
        showError('حدث خطأ أثناء فتح صفحة التعديل');
    }
}

// وظيفة حذف قالب التسعير
function deleteTemplate(templateId) {
    try {
        console.log('🗑️ حذف قالب التسعير:', templateId);

        if (!templateId) {
            console.error('❌ معرف القالب مطلوب للحذف');
            showError('معرف القالب غير صحيح');
            return;
        }

        const template = window.templatesData.templates.find(t => t.id == templateId);

        if (!template) {
            console.error(`❌ لم يتم العثور على القالب: ${templateId}`);
            showError('لم يتم العثور على القالب المطلوب');
            return;
        }

        console.log('✅ تم العثور على القالب للحذف:', template);

        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'حذف قالب التسعير؟',
                text: `هل أنت متأكد من حذف قالب "${template.name}"؟ لا يمكن التراجع عن هذا الإجراء.`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'نعم، احذف القالب',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    performTemplateDelete(templateId, template.name);
                }
            });
        } else {
            console.warn('⚠️ SweetAlert غير متوفر، استخدام confirm عادي');
            if (confirm(`هل أنت متأكد من حذف قالب "${template.name}"؟`)) {
                performTemplateDelete(templateId, template.name);
            }
        }
    } catch (error) {
        console.error('❌ خطأ في حذف القالب:', error);
        showError('حدث خطأ أثناء حذف القالب');
    }
}

// تنفيذ عملية حذف القالب
function performTemplateDelete(templateId, templateName) {
    try {
        console.log('🗑️ تنفيذ حذف القالب:', templateId, templateName);

        // عرض مؤشر التحميل
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'جاري حذف القالب...',
                text: 'يرجى الانتظار',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
        }

        // إرسال طلب الحذف إلى الخادم
        fetch(`${window.templatesRoutes.delete}/${templateId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('✅ تم حذف القالب من الخادم بنجاح:', data);

                // إزالة الصف من الجدول مع تأثير بصري
                const row = document.querySelector(`tr[data-template-id="${templateId}"]`);
                if (row) {
                    row.style.transition = 'opacity 0.3s ease';
                    row.style.opacity = '0';

                    setTimeout(() => {
                        row.remove();
                        updateTableNumbers();
                        updateTableStats();

                        // التحقق من وجود قوالب متبقية
                        const remainingRows = document.querySelectorAll('#templates-table-body tr');
                        if (remainingRows.length === 0) {
                            showEmptyState();
                        }
                    }, 300);
                }

                // إزالة القالب من البيانات المحلية
                const templateIndex = window.templatesData.templates.findIndex(t => t.id == templateId);
                if (templateIndex !== -1) {
                    window.templatesData.templates.splice(templateIndex, 1);
                    window.templatesData.totalCount--;
                }

                // عرض رسالة نجاح
                showSuccess(data.message || `تم حذف قالب "${templateName}" بنجاح`);
            } else {
                console.error('❌ فشل في حذف القالب:', data);
                showError(data.message || 'فشل في حذف القالب');
            }
        })
        .catch(error => {
            console.error('❌ خطأ في طلب حذف القالب:', error);
            showError('حدث خطأ أثناء حذف القالب');
        });
    } catch (error) {
        console.error('❌ خطأ في تنفيذ حذف القالب:', error);
        showError('حدث خطأ أثناء حذف القالب');
    }
}

// وظائف مساعدة لإدارة الواجهة
function showError(message) {
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            title: 'خطأ',
            text: message,
            icon: 'error',
            confirmButtonText: 'موافق',
            confirmButtonColor: '#dc3545'
        });
    } else {
        alert(`خطأ: ${message}`);
    }
}

function showSuccess(message) {
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            title: 'نجح',
            text: message,
            icon: 'success',
            timer: 2000,
            showConfirmButton: false
        });
    } else {
        alert(`نجح: ${message}`);
    }
}

function showEmptyState() {
    const emptyState = document.getElementById('empty-state');
    const tableCard = document.getElementById('templates-table-card');

    if (emptyState) emptyState.style.display = 'block';
    if (tableCard) tableCard.style.display = 'none';
}

function updateTableNumbers() {
    const rows = document.querySelectorAll('#templates-table-body tr');
    rows.forEach((row, index) => {
        const numberBadge = row.querySelector('.badge.bg-light');
        if (numberBadge) {
            numberBadge.textContent = index + 1;
        }
    });
}

function updateTableStats() {
    const remainingCount = document.querySelectorAll('#templates-table-body tr').length;
    const showingCount = document.getElementById('showing-count');
    const totalCount = document.getElementById('total-count');

    if (showingCount) showingCount.textContent = remainingCount;
    if (totalCount) totalCount.textContent = remainingCount;
}

// وظيفة ترتيب الجدول
function sortTable(column) {
    console.log(`🔄 ترتيب جدول قوالب التسعير حسب: ${column}`);

    const tbody = document.getElementById('templates-table-body');
    if (!tbody) return;

    const rows = Array.from(tbody.querySelectorAll('tr'));
    if (rows.length === 0) return;

    let sortFunction;
    switch(column) {
        case 'name':
            sortFunction = (a, b) => {
                const nameA = a.cells[1]?.textContent?.trim() || '';
                const nameB = b.cells[1]?.textContent?.trim() || '';
                return nameA.localeCompare(nameB, 'ar');
            };
            break;
        case 'status':
            sortFunction = (a, b) => {
                const statusA = a.cells[3]?.textContent?.trim() || '';
                const statusB = b.cells[3]?.textContent?.trim() || '';
                return statusA.localeCompare(statusB, 'ar');
            };
            break;
        case 'created_at':
            sortFunction = (a, b) => {
                const dateA = new Date(a.cells[4]?.textContent?.trim() || 0);
                const dateB = new Date(b.cells[4]?.textContent?.trim() || 0);
                return dateB - dateA;
            };
            break;
        default:
            console.warn(`نوع ترتيب غير مدعوم: ${column}`);
            return;
    }

    // تطبيق الترتيب مع تأثير بصري
    tbody.style.opacity = '0.7';
    tbody.style.transition = 'opacity 0.3s ease';

    setTimeout(() => {
        rows.sort(sortFunction);
        rows.forEach(row => tbody.appendChild(row));
        tbody.style.opacity = '1';
        console.log(`✅ تم ترتيب ${rows.length} قالب حسب ${column}`);
    }, 150);
}

// وظائف الإجراءات المجمعة
function bulkActivate() {
    const selectedTemplates = getSelectedTemplates();
    if (selectedTemplates.length === 0) {
        showError(window.templatesMessages.info.selectTemplates);
        return;
    }

    if (typeof Swal !== 'undefined') {
        Swal.fire({
            title: 'تفعيل القوالب المحددة؟',
            text: window.templatesMessages.confirm.bulkActivate,
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'نعم، فعل القوالب',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                console.log('تفعيل القوالب:', selectedTemplates);
                showSuccess('تم تفعيل القوالب المحددة بنجاح');
            }
        });
    }
}

function bulkDeactivate() {
    const selectedTemplates = getSelectedTemplates();
    if (selectedTemplates.length === 0) {
        showError(window.templatesMessages.info.selectTemplates);
        return;
    }

    if (typeof Swal !== 'undefined') {
        Swal.fire({
            title: 'إلغاء تفعيل القوالب المحددة؟',
            text: window.templatesMessages.confirm.bulkDeactivate,
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'نعم، ألغ التفعيل',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                console.log('إلغاء تفعيل القوالب:', selectedTemplates);
                showSuccess('تم إلغاء تفعيل القوالب المحددة بنجاح');
            }
        });
    }
}

function bulkDelete() {
    const selectedTemplates = getSelectedTemplates();
    if (selectedTemplates.length === 0) {
        showError(window.templatesMessages.info.selectTemplates);
        return;
    }

    if (typeof Swal !== 'undefined') {
        Swal.fire({
            title: 'حذف القوالب المحددة؟',
            text: window.templatesMessages.confirm.bulkDelete,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'نعم، احذف القوالب',
            cancelButtonText: 'إلغاء',
            confirmButtonColor: '#dc3545'
        }).then((result) => {
            if (result.isConfirmed) {
                selectedTemplates.forEach(templateId => {
                    const row = document.querySelector(`tr[data-template-id="${templateId}"]`);
                    if (row) row.remove();
                });
                updateTableNumbers();
                updateTableStats();
                showSuccess('تم حذف القوالب المحددة بنجاح');
            }
        });
    }
}

function getSelectedTemplates() {
    const checkboxes = document.querySelectorAll('.template-checkbox:checked');
    return Array.from(checkboxes).map(cb => cb.closest('tr').dataset.templateId);
}

function toggleBulkActions() {
    const selectedCount = document.querySelectorAll('.template-checkbox:checked').length;
    const bulkActions = document.getElementById('bulkActions');

    if (bulkActions) {
        bulkActions.style.display = selectedCount > 0 ? 'block' : 'none';
    }
}

// تهيئة الصفحة عند التحميل
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تم تحميل صفحة قوالب التسعير الموحد بنجاح');

    // تهيئة أزرار الإنشاء
    const createBtnHeader = document.getElementById('create-template-btn-header');
    const createBtnEmpty = document.getElementById('create-template-btn-empty');

    if (createBtnHeader) {
        createBtnHeader.addEventListener('click', handleCreateTemplate);
    }
    if (createBtnEmpty) {
        createBtnEmpty.addEventListener('click', handleCreateTemplate);
    }

    // تهيئة أزرار الإجراءات في الجدول
    document.addEventListener('click', function(e) {
        if (e.target.closest('.preview-btn')) {
            const templateId = e.target.closest('.preview-btn').dataset.id;
            showTemplatePreview(templateId);
        }

        if (e.target.closest('.edit-btn')) {
            const templateId = e.target.closest('.edit-btn').dataset.id;
            editTemplate(templateId);
        }

        if (e.target.closest('.delete-btn')) {
            const templateId = e.target.closest('.delete-btn').dataset.id;
            deleteTemplate(templateId);
        }

        if (e.target.closest('.toggle-btn')) {
            const templateId = e.target.closest('.toggle-btn').dataset.id;
            const currentStatus = e.target.closest('.toggle-btn').dataset.status;
            // هنا يمكن إضافة وظيفة تغيير الحالة
            console.log('تغيير حالة القالب:', templateId, currentStatus);
        }
    });

    // تهيئة تحديد الكل
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.template-checkbox');
            checkboxes.forEach(cb => cb.checked = this.checked);
            toggleBulkActions();
        });
    }

    // تهيئة checkboxes الفردية
    document.querySelectorAll('.template-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', toggleBulkActions);
    });

    // تهيئة أزرار الإجراءات المجمعة
    const bulkActivateBtn = document.getElementById('bulk-activate-btn');
    const bulkDeactivateBtn = document.getElementById('bulk-deactivate-btn');
    const bulkDeleteBtn = document.getElementById('bulk-delete-btn');

    if (bulkActivateBtn) bulkActivateBtn.addEventListener('click', bulkActivate);
    if (bulkDeactivateBtn) bulkDeactivateBtn.addEventListener('click', bulkDeactivate);
    if (bulkDeleteBtn) bulkDeleteBtn.addEventListener('click', bulkDelete);

    // تهيئة ترتيب الجدول
    document.querySelectorAll('th[data-sort]').forEach(header => {
        header.addEventListener('click', function() {
            const column = this.dataset.sort;
            sortTable(column);
        });
        header.style.cursor = 'pointer';
    });

    // تهيئة التبويبات
    document.querySelectorAll('#templateTabs .nav-link').forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault();

            // إزالة الفئة النشطة من جميع التبويبات
            document.querySelectorAll('#templateTabs .nav-link').forEach(t => t.classList.remove('active'));

            // إضافة الفئة النشطة للتبويب المحدد
            this.classList.add('active');

            // تطبيق الفلتر
            const filter = this.dataset.filter;
            filterTemplatesByTab(filter);
        });
    });

    // تهيئة البحث والفلاتر
    const searchInput = document.getElementById('search-input');
    const categoryFilter = document.getElementById('category-filter');
    const statusFilter = document.getElementById('status-filter');
    const sortFilter = document.getElementById('sort-filter');
    const resetBtn = document.getElementById('reset-filters-btn');

    if (searchInput) {
        searchInput.addEventListener('input', debounce(applyFilters, 300));
    }
    if (categoryFilter) {
        categoryFilter.addEventListener('change', applyFilters);
    }
    if (statusFilter) {
        statusFilter.addEventListener('change', applyFilters);
    }
    if (sortFilter) {
        sortFilter.addEventListener('change', function() {
            sortTable(this.value);
        });
    }
    if (resetBtn) {
        resetBtn.addEventListener('click', resetFilters);
    }

    // عرض الجدول أو الحالة الفارغة
    if (window.templatesData.totalCount === 0) {
        showEmptyState();
    } else {
        const tableCard = document.getElementById('templates-table-card');
        if (tableCard) tableCard.style.display = 'block';
    }

    console.log('✅ تم تهيئة جميع وظائف قوالب التسعير');
});

// وظائف الفلترة والبحث
function filterTemplatesByTab(filter) {
    try {
        console.log('🔍 فلترة القوالب حسب التبويب:', filter);

        const rows = document.querySelectorAll('#templates-table-body tr');
        let visibleCount = 0;

        if (!rows.length) {
            console.warn('⚠️ لم يتم العثور على صفوف في الجدول');
            return;
        }

        rows.forEach((row, index) => {
            try {
                let shouldShow = true;
                const templateId = row.dataset.templateId;

                if (filter !== 'all') {
                    // البحث عن القالب في البيانات المحملة
                    const template = window.templatesData.templates.find(t => t.id == templateId);

                    if (template) {
                        switch(filter) {
                            case 'popular':
                                shouldShow = template.category === 'popular';
                                break;
                            case 'recommended':
                                shouldShow = template.category === 'recommended';
                                break;
                            case 'custom':
                                shouldShow = template.category === 'custom' || !template.is_predefined;
                                break;
                            case 'recent':
                                // عرض القوالب المضافة في آخر 30 يوم
                                const createdDate = new Date(template.created_at);
                                const thirtyDaysAgo = new Date();
                                thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                                shouldShow = createdDate >= thirtyDaysAgo;
                                break;
                            default:
                                shouldShow = true;
                        }
                    } else {
                        console.warn(`⚠️ لم يتم العثور على بيانات القالب: ${templateId}`);
                        shouldShow = true; // عرض القالب في حالة عدم وجود بيانات
                    }
                }

                if (shouldShow) {
                    row.style.display = '';
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                }
            } catch (rowError) {
                console.error(`❌ خطأ في معالجة الصف ${index}:`, rowError);
                // عرض الصف في حالة الخطأ
                row.style.display = '';
                visibleCount++;
            }
        });

        updateVisibilityState(visibleCount);
        console.log(`✅ تم فلترة ${visibleCount} قالب من أصل ${rows.length} باستخدام فلتر: ${filter}`);

    } catch (error) {
        console.error('❌ خطأ في فلترة القوالب حسب التبويب:', error);
        // في حالة الخطأ، عرض جميع الصفوف
        const rows = document.querySelectorAll('#templates-table-body tr');
        rows.forEach(row => row.style.display = '');
        updateVisibilityState(rows.length);
    }
}

function applyFilters() {
    try {
        console.log('🔍 تطبيق الفلاتر');

        const searchInput = document.getElementById('search-input');
        const categoryFilterEl = document.getElementById('category-filter');
        const statusFilterEl = document.getElementById('status-filter');

        const searchTerm = searchInput?.value.toLowerCase().trim() || '';
        const categoryFilter = categoryFilterEl?.value || '';
        const statusFilter = statusFilterEl?.value || '';

        console.log('🔍 معايير الفلترة:', { searchTerm, categoryFilter, statusFilter });

        const rows = document.querySelectorAll('#templates-table-body tr');
        let visibleCount = 0;

        if (!rows.length) {
            console.warn('⚠️ لم يتم العثور على صفوف في الجدول للفلترة');
            return;
        }

        rows.forEach((row, index) => {
            try {
                const templateId = row.dataset.templateId;
                const name = row.cells[1]?.textContent?.toLowerCase().trim() || '';
                const description = row.cells[2]?.textContent?.toLowerCase().trim() || '';
                const statusCell = row.cells[3]?.textContent?.toLowerCase().trim() || '';

                let shouldShow = true;

                // فلترة البحث
                if (searchTerm) {
                    const matchesSearch = name.includes(searchTerm) ||
                                        description.includes(searchTerm) ||
                                        templateId?.toLowerCase().includes(searchTerm);
                    if (!matchesSearch) {
                        shouldShow = false;
                    }
                }

                // فلترة الفئة
                if (categoryFilter && shouldShow) {
                    const template = window.templatesData.templates.find(t => t.id == templateId);
                    if (template) {
                        if (template.category !== categoryFilter) {
                            shouldShow = false;
                        }
                    }
                }

                // فلترة الحالة
                if (statusFilter && shouldShow) {
                    const isActive = statusCell.includes('active') || statusCell.includes('نشط');
                    if ((statusFilter === 'active' && !isActive) ||
                        (statusFilter === 'inactive' && isActive)) {
                        shouldShow = false;
                    }
                }

                if (shouldShow) {
                    row.style.display = '';
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                }
            } catch (rowError) {
                console.error(`❌ خطأ في معالجة الصف ${index} أثناء الفلترة:`, rowError);
                // عرض الصف في حالة الخطأ
                row.style.display = '';
                visibleCount++;
            }
        });

        updateVisibilityState(visibleCount);
        console.log(`✅ تم فلترة ${visibleCount} قالب من أصل ${rows.length}`);

    } catch (error) {
        console.error('❌ خطأ في تطبيق الفلاتر:', error);
        // في حالة الخطأ، عرض جميع الصفوف
        const rows = document.querySelectorAll('#templates-table-body tr');
        rows.forEach(row => row.style.display = '');
        updateVisibilityState(rows.length);
    }
}

function resetFilters() {
    console.log('🔄 إعادة تعيين الفلاتر');

    // إعادة تعيين عناصر البحث والفلاتر
    const searchInput = document.getElementById('search-input');
    const categoryFilter = document.getElementById('category-filter');
    const statusFilter = document.getElementById('status-filter');
    const sortFilter = document.getElementById('sort-filter');

    if (searchInput) searchInput.value = '';
    if (categoryFilter) categoryFilter.value = '';
    if (statusFilter) statusFilter.value = '';
    if (sortFilter) sortFilter.value = 'name';

    // إعادة تعيين التبويبات
    document.querySelectorAll('#templateTabs .nav-link').forEach(link => {
        link.classList.remove('active');
        if (link.dataset.filter === 'all') {
            link.classList.add('active');
        }
    });

    // إظهار جميع الصفوف
    document.querySelectorAll('#templates-table-body tr').forEach(row => {
        row.style.display = '';
    });

    updateVisibilityState(document.querySelectorAll('#templates-table-body tr').length);
}

function updateVisibilityState(visibleCount) {
    const emptyState = document.getElementById('empty-state');
    const noResultsState = document.getElementById('no-results-state');
    const tableCard = document.getElementById('templates-table-card');

    if (visibleCount === 0) {
        if (tableCard) tableCard.style.display = 'none';
        if (noResultsState) noResultsState.style.display = 'block';
        if (emptyState) emptyState.style.display = 'none';
    } else {
        if (tableCard) tableCard.style.display = 'block';
        if (noResultsState) noResultsState.style.display = 'none';
        if (emptyState) emptyState.style.display = 'none';
    }
}

// وظيفة مساعدة للتأخير (debounce)
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// تسجيل تحميل الوظائف
console.log('✅ تم تحميل جميع وظائف قوالب التسعير الأساسية');
</script>
@endpush
